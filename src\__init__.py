"""
PYEMC_EPOS Driver Library

A modular Python driver library for controlling EMC Partner generators using the EPOS protocol.
"""

__version__ = "0.1.0"
__author__ = "PyEMC Team"
__email__ = "<EMAIL>"

# Import main components
from .pyepos_driver_lib.connection import DeviceConnection, ConnectionConfig
from .pyepos_driver_lib.commands import EPOSCommands, TestStandard, TestParameters
from .pyepos_driver_lib.drivers import *
from .pyepos_driver_lib.logger import Error<PERSON>and<PERSON>, initialize_logging


__all__ = [
    'DeviceConnection',
    'ConnectionConfig',
    'EPOSCommands',
    'TestStandard',
    'TestParameters',
    'ErrorHandler',
    'initialize_logging',
    'ProfileManager',
    'BaseEPOSDriver',
    'IMU3000Driver',
    'IMU4000Driver',
    'IMUMGDriver',
    'DOW3000Driver',
    'DOWCGDriver',
    'DeviceModel',
    'create_driver'
]
