"""
Setup script for PyEPOS Driver Library
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements from requirements.txt
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="pyemc-epos",
    version="0.1.0",
    author="PyEMC Team",
    author_email="<EMAIL>",
    description="A modular Python driver library for controlling EMC Partner generators using the EPOS protocol",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/PYEMC_EPOS",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/PYEMC_EPOS/issues",
        "Documentation": "https://your-docs-link.com",
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-cov>=4.0",
            "black>=22.0",
            "flake8>=5.0",
            "mypy>=1.0",
        ],
        "docs": [
            "sphinx>=5.0",
            "sphinx-rtd-theme>=1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pyepos=pyepos_driver_lib.main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
