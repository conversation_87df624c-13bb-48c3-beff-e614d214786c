# Core dependencies
pyyaml>=6.0          # For YAML-based test profile management
requests>=2.28.0     # For HTTP communication (if needed)
click>=8.0.0         # For CLI interface
colorama>=0.4.0      # For colored terminal output
typing-extensions>=4.0.0  # For enhanced type hints

# Networking and communication
socket-io>=0.5.0     # For real-time communication (if needed)

# Data handling
pandas>=1.5.0        # For data analysis and reporting
numpy>=1.21.0        # For numerical operations

# Logging and monitoring
# Using built-in logging module with custom ErrorHandler

# Configuration management
python-dotenv>=0.19.0  # For environment variable management

# Development dependencies (install with pip install -e .[dev])
# pytest>=7.0
# pytest-cov>=4.0
# black>=22.0
# flake8>=5.0
# mypy>=1.0

# Documentation dependencies (install with pip install -e .[docs])
# sphinx>=5.0
# sphinx-rtd-theme>=1.0
