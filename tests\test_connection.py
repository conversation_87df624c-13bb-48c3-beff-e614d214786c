"""
Unit tests for the EPOS connection module.
"""

import pytest
import socket
from unittest.mock import Mock, patch, MagicMock
import time

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyepos_driver_lib.connection import (
    ConnectionConfig, 
    EPOSConnection, 
    EPOSConnectionError, 
    EPOSTimeoutError
)


class TestConnectionConfig:
    """Test ConnectionConfig dataclass."""
    
    def test_default_values(self):
        """Test default configuration values."""
        config = ConnectionConfig(host="*************")
        
        assert config.host == "*************"
        assert config.port == 5025
        assert config.timeout == 10.0
        assert config.retry_attempts == 3
        assert config.retry_delay == 1.0
        assert config.keepalive is True
        assert config.buffer_size == 4096
    
    def test_custom_values(self):
        """Test custom configuration values."""
        config = ConnectionConfig(
            host="********",
            port=8080,
            timeout=5.0,
            retry_attempts=5,
            retry_delay=2.0,
            keepalive=False,
            buffer_size=8192
        )
        
        assert config.host == "********"
        assert config.port == 8080
        assert config.timeout == 5.0
        assert config.retry_attempts == 5
        assert config.retry_delay == 2.0
        assert config.keepalive is False
        assert config.buffer_size == 8192


class TestEPOSConnection:
    """Test EPOSConnection class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = ConnectionConfig(
            host="*************",
            port=5025,
            timeout=1.0,
            retry_attempts=1,
            retry_delay=0.1
        )
        self.connection = EPOSConnection(self.config)
    
    def test_initialization(self):
        """Test connection initialization."""
        assert self.connection.config == self.config
        assert self.connection._socket is None
        assert self.connection._connected is False
    
    def test_is_connected_false_initially(self):
        """Test is_connected returns False initially."""
        assert not self.connection.is_connected()
    
    @patch('socket.socket')
    def test_connect_success(self, mock_socket_class):
        """Test successful connection."""
        # Mock socket
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        
        # Test connection
        result = self.connection.connect()
        
        assert result is True
        assert self.connection._connected is True
        assert self.connection.is_connected() is True
        
        # Verify socket configuration
        mock_socket.settimeout.assert_called_with(self.config.timeout)
        mock_socket.setsockopt.assert_called_with(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        mock_socket.connect.assert_called_with((self.config.host, self.config.port))
    
    @patch('socket.socket')
    def test_connect_failure(self, mock_socket_class):
        """Test connection failure."""
        # Mock socket that raises exception
        mock_socket = Mock()
        mock_socket.connect.side_effect = socket.error("Connection refused")
        mock_socket_class.return_value = mock_socket
        
        # Test connection
        with pytest.raises(EPOSConnectionError):
            self.connection.connect()
        
        assert self.connection._connected is False
        assert not self.connection.is_connected()
    
    @patch('socket.socket')
    def test_connect_retry_logic(self, mock_socket_class):
        """Test connection retry logic."""
        # Configure for multiple retries
        self.config.retry_attempts = 3
        self.connection = EPOSConnection(self.config)
        
        # Mock socket that fails first two attempts, succeeds on third
        mock_socket = Mock()
        mock_socket.connect.side_effect = [
            socket.error("Connection refused"),
            socket.error("Connection refused"),
            None  # Success on third attempt
        ]
        mock_socket_class.return_value = mock_socket
        
        with patch('time.sleep'):  # Speed up test
            result = self.connection.connect()
        
        assert result is True
        assert mock_socket.connect.call_count == 3
    
    def test_disconnect(self):
        """Test disconnection."""
        # Mock connected state
        mock_socket = Mock()
        self.connection._socket = mock_socket
        self.connection._connected = True
        
        self.connection.disconnect()
        
        mock_socket.close.assert_called_once()
        assert self.connection._socket is None
        assert self.connection._connected is False
    
    def test_disconnect_with_exception(self):
        """Test disconnection when socket.close() raises exception."""
        # Mock socket that raises exception on close
        mock_socket = Mock()
        mock_socket.close.side_effect = Exception("Close error")
        self.connection._socket = mock_socket
        self.connection._connected = True
        
        # Should not raise exception
        self.connection.disconnect()
        
        assert self.connection._socket is None
        assert self.connection._connected is False
    
    def test_send_command_not_connected(self):
        """Test sending command when not connected."""
        with pytest.raises(EPOSConnectionError, match="Not connected"):
            self.connection.send_command("*IDN?")
    
    @patch('socket.socket')
    def test_send_command_success(self, mock_socket_class):
        """Test successful command sending."""
        # Setup connected state
        mock_socket = Mock()
        mock_socket.recv.return_value = b"EMC Partner,IMU3000,12345,1.0\n"
        mock_socket_class.return_value = mock_socket
        
        self.connection.connect()
        
        # Send command
        response = self.connection.send_command("*IDN?")
        
        assert response == "EMC Partner,IMU3000,12345,1.0"
        mock_socket.sendall.assert_called_with(b"*IDN?\n")
    
    @patch('socket.socket')
    def test_send_command_timeout(self, mock_socket_class):
        """Test command timeout."""
        # Setup connected state
        mock_socket = Mock()
        mock_socket.sendall.side_effect = socket.timeout()
        mock_socket_class.return_value = mock_socket
        
        self.connection.connect()
        
        # Send command
        with pytest.raises(EPOSTimeoutError):
            self.connection.send_command("*IDN?")
    
    @patch('socket.socket')
    def test_send_command_socket_error(self, mock_socket_class):
        """Test command socket error."""
        # Setup connected state
        mock_socket = Mock()
        mock_socket.sendall.side_effect = socket.error("Connection lost")
        mock_socket_class.return_value = mock_socket
        
        self.connection.connect()
        
        # Send command
        with pytest.raises(EPOSConnectionError):
            self.connection.send_command("*IDN?")
        
        # Should mark as disconnected
        assert self.connection._connected is False
    
    @patch('socket.socket')
    def test_get_device_info(self, mock_socket_class):
        """Test getting device information."""
        # Setup connected state
        mock_socket = Mock()
        mock_socket.recv.return_value = b"EMC Partner,IMU3000,SN12345,FW1.0\n"
        mock_socket_class.return_value = mock_socket
        
        self.connection.connect()
        
        # Get device info
        device_info = self.connection.get_device_info()
        
        assert device_info['manufacturer'] == 'EMC Partner'
        assert device_info['model'] == 'IMU3000'
        assert device_info['serial_number'] == 'SN12345'
        assert device_info['firmware_version'] == 'FW1.0'
        assert device_info['host'] == self.config.host
        assert device_info['port'] == self.config.port
    
    @patch('socket.socket')
    def test_context_manager(self, mock_socket_class):
        """Test context manager functionality."""
        mock_socket = Mock()
        mock_socket_class.return_value = mock_socket
        
        with self.connection as conn:
            assert conn is self.connection
            assert self.connection._connected is True
        
        # Should be disconnected after context
        mock_socket.close.assert_called_once()
    
    def test_destructor_cleanup(self):
        """Test destructor cleanup."""
        mock_socket = Mock()
        self.connection._socket = mock_socket
        self.connection._connected = True
        
        # Call destructor
        self.connection.__del__()
        
        mock_socket.close.assert_called_once()
