#!/usr/bin/env python3
"""
PyEPOS Driver Library CLI Interface

Command-line interface for testing and basic operations with EMC generators.
"""

import click
import sys
from pathlib import Path
from typing import Optional
import json

from .connection import ConnectionConfig
from .drivers import DeviceModel, create_driver
from .commands import TestStandard, TestParameters
from .logger import initialize_logging, get_logger


@click.group()
@click.option('--host', default='*************', help='Device IP address')
@click.option('--port', default=5025, help='Device port')
@click.option('--timeout', default=10.0, help='Connection timeout in seconds')
@click.option('--log-level', default='INFO', help='Logging level')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.pass_context
def cli(ctx, host, port, timeout, log_level, verbose):
    """PyEPOS Driver Library CLI - Control EMC Partner generators via EPOS protocol."""
    
    # Initialize logging
    initialize_logging(
        log_level=log_level,
        console_output=True,
        file_output=not verbose  # Disable file logging in verbose mode
    )
    
    # Store configuration in context
    ctx.ensure_object(dict)
    ctx.obj['config'] = ConnectionConfig(
        host=host,
        port=port,
        timeout=timeout
    )
    ctx.obj['verbose'] = verbose


@cli.command()
@click.option('--model', type=click.Choice([m.value for m in DeviceModel]), 
              required=True, help='Device model')
@click.pass_context
def info(ctx, model):
    """Get device information and capabilities."""
    
    config = ctx.obj['config']
    device_model = DeviceModel(model)
    
    click.echo(f"Device Model: {device_model.value}")
    click.echo(f"Host: {config.host}:{config.port}")
    click.echo()
    
    try:
        # Create driver
        driver = create_driver(device_model, config)
        
        # Display capabilities
        capabilities = driver.get_capabilities()
        click.echo("Device Capabilities:")
        click.echo(f"  Max Voltage: {capabilities.max_voltage}V")
        click.echo(f"  Max Current: {capabilities.max_current}A")
        click.echo(f"  Current Control: {capabilities.has_current_control}")
        click.echo(f"  Temperature Monitoring: {capabilities.has_temperature_monitoring}")
        click.echo(f"  Calibration Required: {capabilities.calibration_required}")
        click.echo(f"  Frequency Range: {capabilities.frequency_range[0]}-{capabilities.frequency_range[1]} Hz")
        
        click.echo("  Supported Waveforms:")
        for waveform in capabilities.supported_waveforms:
            click.echo(f"    - {waveform.value}")
        
        click.echo("  Supported Standards:")
        for standard in capabilities.supported_standards:
            click.echo(f"    - {standard.value}")
        
        # Try to connect and get device info
        click.echo("\nConnecting to device...")
        if driver.connect():
            click.echo("✓ Connected successfully!")
            
            device_info = driver.get_device_info()
            click.echo("\nDevice Information:")
            for key, value in device_info.items():
                click.echo(f"  {key}: {value}")
            
            driver.disconnect()
            click.echo("✓ Disconnected")
        else:
            click.echo("✗ Failed to connect")
            
    except Exception as e:
        click.echo(f"✗ Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--model', type=click.Choice([m.value for m in DeviceModel]), 
              required=True, help='Device model')
@click.option('--voltage', type=float, required=True, help='Test voltage in V')
@click.option('--current', type=float, help='Test current in A')
@click.option('--duration', type=float, default=5.0, help='Test duration in seconds')
@click.pass_context
def test(ctx, model, voltage, current, duration):
    """Run a basic test with specified parameters."""
    
    config = ctx.obj['config']
    device_model = DeviceModel(model)
    
    click.echo(f"Running test on {device_model.value}")
    click.echo(f"Voltage: {voltage}V")
    if current:
        click.echo(f"Current: {current}A")
    click.echo(f"Duration: {duration}s")
    click.echo()
    
    try:
        # Create driver
        driver = create_driver(device_model, config)
        
        # Connect
        click.echo("Connecting to device...")
        if not driver.connect():
            click.echo("✗ Failed to connect", err=True)
            sys.exit(1)
        
        click.echo("✓ Connected")
        
        # Set parameters
        click.echo(f"Setting voltage to {voltage}V...")
        if not driver.set_voltage(voltage):
            click.echo("✗ Failed to set voltage", err=True)
            sys.exit(1)
        
        if current and driver.get_capabilities().has_current_control:
            click.echo(f"Setting current to {current}A...")
            if not driver.set_current(current):
                click.echo("✗ Failed to set current", err=True)
                sys.exit(1)
        
        # Start test
        click.echo("Starting test...")
        if not driver.start_test():
            click.echo("✗ Failed to start test", err=True)
            sys.exit(1)
        
        # Wait for duration
        import time
        click.echo(f"Running test for {duration} seconds...")
        time.sleep(duration)
        
        # Stop test
        click.echo("Stopping test...")
        if not driver.stop_test():
            click.echo("✗ Failed to stop test", err=True)
        
        click.echo("✓ Test completed successfully")
        
        # Disconnect
        driver.disconnect()
        click.echo("✓ Disconnected")
        
    except Exception as e:
        click.echo(f"✗ Error: {e}", err=True)
        sys.exit(1)











@cli.command()
def version():
    """Show version information."""
    
    try:
        from . import __version__
        click.echo(f"PyEPOS Driver Library v{__version__}")
    except ImportError:
        click.echo("PyEPOS Driver Library (development version)")
    
    click.echo("EMC Partner generator control via EPOS protocol")
    click.echo("https://github.com/your-org/PYEMC_EPOS")


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
