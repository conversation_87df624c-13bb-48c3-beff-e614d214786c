# PYEMC_EPOS Driver Library

<https://your-hosted-image-url.com/logo.png> <!-- Replace with actual hosted image -->

## 📦 Overview

`PYEMC_EPOS` is a modular Python 3.10+ driver library for controlling EMC Partner generators using the EPOS protocol. It enables engineers and technicians to automate EMC testing workflows via a scriptable interface, replacing manual front-panel HMI interaction.

This module is designed to be imported into other Python projects, allowing users to build custom test sequences, manage metadata, and generate protocol reports.

> **EPOS** stands for **EMC PARTNER Operating System**, the command protocol used by supported EMC generators.

---

## 🧰 Features

- TCP/IP communication with EMC generators
- EPOS command formatting and response parsing
- Device metadata retrieval (model, firmware, serial number)
- Test profile orchestration
- Centralized logging and error handling
- Modular architecture for easy extension

---

## 🧪 Supported Devices

- IMU3000
- IMU4000
- IMU-MG1
- DOW3000
- DOW-CG1

## ⚡ Supported Test Standards

- **IEC 61000-4-5:2014** Surge Immunity
  - **Surge** (1.2/50 µs Voltage, 8/20 µs Current)
  - **Telecom Surge** (10/700 µs Voltage, 5/320 µs Current)
- **IEC 61000-4-4:2012** Electrical Fast Transients (EFT)
- **IEC 61000-4-12:2017** Ring Wave Immunity
- **IEC 61000-4-18:2019** Damped Oscillatory Waves (Slow & Fast)

---

## 👨‍🔧 Target Users

- **Power Users**: Extend functionality via custom Python scripts

---

## 🚀 Quick Start

```bash
# Install from PyPI (once published)
pip install pyemc-epos

# Or clone the repo
git clone https://github.com/your-org/PYEMC_EPOS.git
cd PYEMC_EPOS/pyepos_driver_lib

# Install dependencies
pip install -r requirements.txt

# Run the CLI (for testing)
python main.py
```

---

## 🧱 Project Structure

> This section can be revised for simplification if possible

```plaintext
PYEMC_EPOS/
├── src/
│   ├── __init__.py
│   └── pyepos_driver_lib/
│       ├── connection.py
│       ├── commands.py
│       ├── drivers.py
│       └── logger.py
├── unit_tests/
│   ├── test_connection.py
│   ├── test_commands.py
│   ├── test_drivers.py
│   └── test_logger.py
├── examples/
│   └── test_driver.py
├── __init__.py
├── README.md
├── setup.py
├── requirements.txt
└── docs/
```

---

## 🧭 Design Principles

DRY: Shared logic is centralized
YAGNI: Only essential features implemented early
Safety-Critical Coding: Logging, error handling, and validation prioritized
Modular Architecture: Easy to extend and maintain

### Centralized Logging Example

look at logging.py file

## 📄 License

MIT License

## 📚 Documentation

Full documentation is available <https://your-docs-link.com>

## 🤝 Contributing

Issues and feature requests are welcome via GitHub. Please follow the contribution guidelines in CONTRIBUTING.md.
