"""
Profile Manager Mo<PERSON><PERSON>

Handles YAML-based test profile management and orchestration for EMC testing.
"""

import yaml
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import uuid

from .commands import TestStandard, TestParameters, WaveformType
from .drivers import BaseEPOSDriver, DeviceModel, create_driver
from .connection import ConnectionConfig
from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class TestStep:
    """Individual test step configuration."""
    name: str
    description: str
    standard: TestStandard
    parameters: TestParameters
    duration: float  # Duration in seconds
    delay_before: float = 0.0  # Delay before step in seconds
    delay_after: float = 0.0   # Delay after step in seconds
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for YAML serialization."""
        return {
            'name': self.name,
            'description': self.description,
            'standard': self.standard.value,
            'parameters': asdict(self.parameters),
            'duration': self.duration,
            'delay_before': self.delay_before,
            'delay_after': self.delay_after,
            'enabled': self.enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestStep':
        """Create from dictionary loaded from YAML."""
        return cls(
            name=data['name'],
            description=data['description'],
            standard=TestStandard(data['standard']),
            parameters=TestParameters(**data['parameters']),
            duration=data['duration'],
            delay_before=data.get('delay_before', 0.0),
            delay_after=data.get('delay_after', 0.0),
            enabled=data.get('enabled', True)
        )


@dataclass
class TestProfile:
    """Complete test profile configuration."""
    name: str
    description: str
    version: str
    device_model: DeviceModel
    connection_config: ConnectionConfig
    steps: List[TestStep]
    metadata: Dict[str, Any]
    created_at: datetime
    modified_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for YAML serialization."""
        return {
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'device_model': self.device_model.value,
            'connection_config': {
                'host': self.connection_config.host,
                'port': self.connection_config.port,
                'timeout': self.connection_config.timeout,
                'retry_attempts': self.connection_config.retry_attempts,
                'retry_delay': self.connection_config.retry_delay
            },
            'steps': [step.to_dict() for step in self.steps],
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat(),
            'modified_at': self.modified_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestProfile':
        """Create from dictionary loaded from YAML."""
        connection_config = ConnectionConfig(**data['connection_config'])
        steps = [TestStep.from_dict(step_data) for step_data in data['steps']]
        
        return cls(
            name=data['name'],
            description=data['description'],
            version=data['version'],
            device_model=DeviceModel(data['device_model']),
            connection_config=connection_config,
            steps=steps,
            metadata=data.get('metadata', {}),
            created_at=datetime.fromisoformat(data['created_at']),
            modified_at=datetime.fromisoformat(data['modified_at'])
        )


class ProfileManagerError(Exception):
    """Exception raised for profile manager errors."""
    pass


class ProfileManager:
    """
    Manages test profiles and orchestrates test execution.
    
    Handles loading, saving, and executing YAML-based test profiles.
    """
    
    def __init__(self, profile_directory: Union[str, Path] = "profiles"):
        """
        Initialize profile manager.
        
        Args:
            profile_directory: Directory containing test profiles
        """
        self.profile_directory = Path(profile_directory)
        self.profile_directory.mkdir(exist_ok=True)
        
        self.current_profile: Optional[TestProfile] = None
        self.current_driver: Optional[BaseEPOSDriver] = None
        self.execution_log: List[Dict[str, Any]] = []
        
        logger.info(f"Profile manager initialized with directory: {str(self.profile_directory)}")
    
    def create_profile(self, 
                      name: str,
                      description: str,
                      device_model: DeviceModel,
                      connection_config: ConnectionConfig,
                      version: str = "1.0") -> TestProfile:
        """
        Create a new test profile.
        
        Args:
            name: Profile name
            description: Profile description
            device_model: Target device model
            connection_config: Connection configuration
            version: Profile version
            
        Returns:
            New test profile
        """
        now = datetime.now()
        
        profile = TestProfile(
            name=name,
            description=description,
            version=version,
            device_model=device_model,
            connection_config=connection_config,
            steps=[],
            metadata={
                'created_by': 'PyEPOS Profile Manager',
                'profile_id': str(uuid.uuid4())
            },
            created_at=now,
            modified_at=now
        )
        
        logger.info(f"Created new test profile '{name}' for device {device_model.value}")
        
        return profile
    
    def add_test_step(self, 
                     profile: TestProfile,
                     name: str,
                     description: str,
                     standard: TestStandard,
                     parameters: TestParameters,
                     duration: float,
                     **kwargs) -> TestStep:
        """
        Add a test step to a profile.
        
        Args:
            profile: Target profile
            name: Step name
            description: Step description
            standard: Test standard
            parameters: Test parameters
            duration: Step duration in seconds
            **kwargs: Additional step parameters
            
        Returns:
            Created test step
        """
        step = TestStep(
            name=name,
            description=description,
            standard=standard,
            parameters=parameters,
            duration=duration,
            **kwargs
        )
        
        profile.steps.append(step)
        profile.modified_at = datetime.now()
        
        logger.info(f"Added test step '{name}' ({standard.value}) to profile '{profile.name}'")
        
        return step
    
    def save_profile(self, profile: TestProfile, filename: Optional[str] = None) -> Path:
        """
        Save profile to YAML file.
        
        Args:
            profile: Profile to save
            filename: Optional filename (defaults to profile name)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            filename = f"{profile.name.replace(' ', '_').lower()}.yaml"
        
        file_path = self.profile_directory / filename
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(profile.to_dict(), f, default_flow_style=False, indent=2)
            
            logger.info(f"Profile '{profile.name}' saved to {str(file_path)}")
            
            return file_path
            
        except Exception as e:
            raise ProfileManagerError(f"Failed to save profile: {e}")
    
    def load_profile(self, filename: Union[str, Path]) -> TestProfile:
        """
        Load profile from YAML file.
        
        Args:
            filename: Path to profile file
            
        Returns:
            Loaded test profile
        """
        file_path = Path(filename)
        if not file_path.is_absolute():
            file_path = self.profile_directory / file_path
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            profile = TestProfile.from_dict(data)
            
            logger.info(f"Profile '{profile.name}' loaded from {str(file_path)} with {len(profile.steps)} steps")
            
            return profile
            
        except Exception as e:
            raise ProfileManagerError(f"Failed to load profile from {file_path}: {e}")
    
    def list_profiles(self) -> List[Dict[str, Any]]:
        """
        List available profiles in the profile directory.
        
        Returns:
            List of profile information dictionaries
        """
        profiles = []
        
        for yaml_file in self.profile_directory.glob("*.yaml"):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                
                profiles.append({
                    'filename': yaml_file.name,
                    'name': data.get('name', 'Unknown'),
                    'description': data.get('description', ''),
                    'version': data.get('version', ''),
                    'device_model': data.get('device_model', 'Unknown'),
                    'step_count': len(data.get('steps', [])),
                    'modified_at': data.get('modified_at', '')
                })
                
            except Exception as e:
                logger.warning(f"Failed to read profile {yaml_file.name}: {str(e)}")
        
        return sorted(profiles, key=lambda x: x['modified_at'], reverse=True)

    def execute_profile(self, profile: TestProfile, dry_run: bool = False) -> Dict[str, Any]:
        """
        Execute a test profile.

        Args:
            profile: Profile to execute
            dry_run: If True, validate profile without executing

        Returns:
            Execution results
        """
        self.current_profile = profile
        self.execution_log.clear()

        execution_id = str(uuid.uuid4())
        start_time = datetime.now()

        logger.info(f"Starting profile execution '{profile.name}' (ID: {execution_id}, dry_run: {dry_run})")

        try:
            # Create and connect to driver
            if not dry_run:
                self.current_driver = create_driver(profile.device_model, profile.connection_config)
                if not self.current_driver.connect():
                    raise ProfileManagerError("Failed to connect to device")

            # Execute steps
            results = []
            for i, step in enumerate(profile.steps):
                if not step.enabled:
                    logger.info(f"Skipping disabled step: {step.name}")
                    continue

                step_result = self._execute_step(step, i + 1, dry_run)
                results.append(step_result)

                # Log step completion
                self.execution_log.append({
                    'step_number': i + 1,
                    'step_name': step.name,
                    'result': step_result,
                    'timestamp': datetime.now().isoformat()
                })

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            execution_result = {
                'execution_id': execution_id,
                'profile_name': profile.name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'total_steps': len(profile.steps),
                'executed_steps': len(results),
                'success': all(r.get('success', False) for r in results),
                'results': results,
                'dry_run': dry_run
            }

            logger.info(f"Profile execution completed (ID: {execution_id}, "
                       f"success: {execution_result['success']}, duration: {duration:.2f}s)")

            return execution_result

        except Exception as e:
            logger.error(f"Profile execution failed (ID: {execution_id}): {str(e)}")
            raise ProfileManagerError(f"Profile execution failed: {e}")

        finally:
            # Cleanup
            if self.current_driver and not dry_run:
                self.current_driver.disconnect()
                self.current_driver = None

    def _execute_step(self, step: TestStep, step_number: int, dry_run: bool) -> Dict[str, Any]:
        """
        Execute a single test step.

        Args:
            step: Test step to execute
            step_number: Step number for logging
            dry_run: If True, validate step without executing

        Returns:
            Step execution result
        """
        logger.info(f"Executing test step {step_number}: {step.name} ({step.standard.value})")

        step_start = datetime.now()

        try:
            if dry_run:
                # Validate step parameters
                self._validate_step(step)
                result = {'success': True, 'message': 'Validation passed'}
            else:
                # Execute actual step
                result = self._run_step(step)

            step_end = datetime.now()
            step_duration = (step_end - step_start).total_seconds()

            return {
                'step_number': step_number,
                'step_name': step.name,
                'standard': step.standard.value,
                'start_time': step_start.isoformat(),
                'end_time': step_end.isoformat(),
                'duration_seconds': step_duration,
                'success': result['success'],
                'message': result.get('message', ''),
                'data': result.get('data', {})
            }

        except Exception as e:
            logger.error(f"Step {step_number} '{step.name}' execution failed: {str(e)}")

            return {
                'step_number': step_number,
                'step_name': step.name,
                'standard': step.standard.value,
                'start_time': step_start.isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_seconds': 0,
                'success': False,
                'message': str(e),
                'data': {}
            }

    def _validate_step(self, step: TestStep) -> None:
        """Validate step parameters."""
        # Check if device supports the test standard
        if self.current_profile:
            driver = create_driver(self.current_profile.device_model,
                                 self.current_profile.connection_config)
            capabilities = driver.get_capabilities()

            if step.standard not in capabilities.supported_standards:
                raise ProfileManagerError(
                    f"Device {self.current_profile.device_model.value} "
                    f"does not support standard {step.standard.value}"
                )

    def _run_step(self, step: TestStep) -> Dict[str, Any]:
        """Run actual test step."""
        if not self.current_driver:
            raise ProfileManagerError("No driver connected")

        try:
            # Apply delay before step
            if step.delay_before > 0:
                time.sleep(step.delay_before)

            # Set test parameters
            self.current_driver.set_voltage(step.parameters.voltage)

            if (step.parameters.current is not None and
                self.current_driver.get_capabilities().has_current_control):
                self.current_driver.set_current(step.parameters.current)

            # Start test
            if not self.current_driver.start_test():
                raise ProfileManagerError("Failed to start test")

            # Wait for test duration
            time.sleep(step.duration)

            # Stop test
            if not self.current_driver.stop_test():
                raise ProfileManagerError("Failed to stop test")

            # Apply delay after step
            if step.delay_after > 0:
                time.sleep(step.delay_after)

            return {
                'success': True,
                'message': 'Step completed successfully',
                'data': {
                    'voltage': step.parameters.voltage,
                    'current': step.parameters.current,
                    'duration': step.duration
                }
            }

        except Exception as e:
            # Ensure test is stopped on error
            try:
                self.current_driver.stop_test()
            except:
                pass
            raise e
