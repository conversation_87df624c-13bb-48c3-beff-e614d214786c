"""
Unit tests for the EPOS drivers module.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyepos_driver_lib.drivers import (
    BaseEPOSDriver,
    IMU3000Driver,
    IMU4000Driver,
    IMUMGDriver,
    DOW3000Driver,
    DOWCGDriver,
    DeviceModel,
    DeviceStatus,
    DeviceCapabilities,
    EPOSDriverError,
    create_driver
)
from pyepos_driver_lib.connection import ConnectionConfig
from pyepos_driver_lib.commands import WaveformType, TestStandard


class TestDeviceCapabilities:
    """Test DeviceCapabilities dataclass."""
    
    def test_capabilities_creation(self):
        """Test creating device capabilities."""
        capabilities = DeviceCapabilities(
            max_voltage=6000.0,
            max_current=3000.0,
            supported_waveforms=[WaveformType.SURGE],
            supported_standards=[TestStandard.IEC_61000_4_5],
            frequency_range=(0.1, 100.0),
            has_current_control=True,
            has_temperature_monitoring=True,
            calibration_required=True
        )
        
        assert capabilities.max_voltage == 6000.0
        assert capabilities.max_current == 3000.0
        assert WaveformType.SURGE in capabilities.supported_waveforms
        assert TestStandard.IEC_61000_4_5 in capabilities.supported_standards
        assert capabilities.frequency_range == (0.1, 100.0)
        assert capabilities.has_current_control is True
        assert capabilities.has_temperature_monitoring is True
        assert capabilities.calibration_required is True


class TestIMU3000Driver:
    """Test IMU3000Driver implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = ConnectionConfig(host="*************")
        self.driver = IMU3000Driver(self.config)
    
    def test_initialization(self):
        """Test driver initialization."""
        assert self.driver.get_model() == DeviceModel.IMU3000
        assert self.driver.status == DeviceStatus.DISCONNECTED
        assert self.driver.connection is None
        assert isinstance(self.driver.capabilities, DeviceCapabilities)
    
    def test_capabilities(self):
        """Test IMU3000 capabilities."""
        capabilities = self.driver.get_capabilities()
        
        assert capabilities.max_voltage == 6000.0
        assert capabilities.max_current == 3000.0
        assert WaveformType.SURGE in capabilities.supported_waveforms
        assert WaveformType.TELECOM in capabilities.supported_waveforms
        assert TestStandard.IEC_61000_4_5 in capabilities.supported_standards
        assert TestStandard.TELECOM_SURGE in capabilities.supported_standards
        assert capabilities.frequency_range == (0.1, 100.0)
        assert capabilities.has_current_control is True
        assert capabilities.calibration_required is True
    
    @patch('pyepos_driver_lib.drivers.EPOSConnection')
    def test_connect_success(self, mock_connection_class):
        """Test successful connection."""
        # Mock connection
        mock_connection = Mock()
        mock_connection.connect.return_value = True
        mock_connection.get_device_info.return_value = {'model': 'IMU3000'}
        mock_connection_class.return_value = mock_connection
        
        # Mock _send_command for initialization
        with patch.object(self.driver, '_send_command') as mock_send:
            mock_send.side_effect = [None, None, {'code': 0}, True]  # reset, clear, error check, opc
            
            result = self.driver.connect()
        
        assert result is True
        assert self.driver.status == DeviceStatus.READY
        assert self.driver.connection is mock_connection
    
    @patch('pyepos_driver_lib.drivers.EPOSConnection')
    def test_connect_failure(self, mock_connection_class):
        """Test connection failure."""
        # Mock connection that fails
        mock_connection = Mock()
        mock_connection.connect.return_value = False
        mock_connection_class.return_value = mock_connection
        
        result = self.driver.connect()
        
        assert result is False
        assert self.driver.status == DeviceStatus.ERROR
    
    def test_disconnect(self):
        """Test disconnection."""
        # Mock connected state
        mock_connection = Mock()
        self.driver.connection = mock_connection
        self.driver.status = DeviceStatus.READY
        
        self.driver.disconnect()
        
        mock_connection.disconnect.assert_called_once()
        assert self.driver.connection is None
        assert self.driver.status == DeviceStatus.DISCONNECTED
    
    def test_is_connected(self):
        """Test connection status check."""
        # Initially not connected
        assert not self.driver.is_connected()
        
        # Mock connected state
        mock_connection = Mock()
        mock_connection.is_connected.return_value = True
        self.driver.connection = mock_connection
        self.driver.status = DeviceStatus.READY
        
        assert self.driver.is_connected()
    
    def test_send_command_not_connected(self):
        """Test sending command when not connected."""
        with pytest.raises(EPOSDriverError, match="not connected"):
            self.driver._send_command('identify')
    
    @patch('pyepos_driver_lib.drivers.EPOSConnection')
    def test_send_command_success(self, mock_connection_class):
        """Test successful command sending."""
        # Setup connected state
        mock_connection = Mock()
        mock_connection.is_connected.return_value = True
        mock_connection.send_command.return_value = "EMC Partner,IMU3000,12345,1.0"
        self.driver.connection = mock_connection
        self.driver.status = DeviceStatus.READY
        
        # Mock commands.format_command and parse_response
        with patch.object(self.driver.commands, 'format_command') as mock_format:
            with patch.object(self.driver.commands, 'parse_response') as mock_parse:
                mock_format.return_value = "*IDN?"
                mock_parse.return_value = "EMC Partner,IMU3000,12345,1.0"
                
                result = self.driver._send_command('identify')
        
        assert result == "EMC Partner,IMU3000,12345,1.0"
        mock_connection.send_command.assert_called_with("*IDN?")
    
    def test_set_voltage_success(self):
        """Test setting voltage successfully."""
        # Mock connected state and _send_command
        self.driver.status = DeviceStatus.READY
        with patch.object(self.driver, '_send_command') as mock_send:
            mock_send.return_value = None
            
            result = self.driver.set_voltage(1000.0)
        
        assert result is True
        mock_send.assert_called_with('set_voltage', value=1000.0)
    
    def test_set_voltage_exceeds_maximum(self):
        """Test setting voltage that exceeds maximum."""
        with pytest.raises(EPOSDriverError, match="exceeds maximum"):
            self.driver.set_voltage(10000.0)  # Exceeds 6kV max
    
    def test_set_current_success(self):
        """Test setting current successfully."""
        # Mock connected state and _send_command
        self.driver.status = DeviceStatus.READY
        with patch.object(self.driver, '_send_command') as mock_send:
            mock_send.return_value = None
            
            result = self.driver.set_current(500.0)
        
        assert result is True
        mock_send.assert_called_with('set_current', value=500.0)
    
    def test_set_current_exceeds_maximum(self):
        """Test setting current that exceeds maximum."""
        with pytest.raises(EPOSDriverError, match="exceeds maximum"):
            self.driver.set_current(5000.0)  # Exceeds 3kA max
    
    def test_start_test(self):
        """Test starting test."""
        # Mock connected state and _send_command
        self.driver.status = DeviceStatus.READY
        with patch.object(self.driver, '_send_command') as mock_send:
            mock_send.return_value = None
            
            result = self.driver.start_test()
        
        assert result is True
        assert self.driver.status == DeviceStatus.TESTING
        mock_send.assert_called_with('start_test')
    
    def test_stop_test(self):
        """Test stopping test."""
        # Mock testing state and _send_command
        self.driver.status = DeviceStatus.TESTING
        with patch.object(self.driver, '_send_command') as mock_send:
            mock_send.return_value = None
            
            result = self.driver.stop_test()
        
        assert result is True
        assert self.driver.status == DeviceStatus.READY
        mock_send.assert_called_with('stop_test')


class TestIMU4000Driver:
    """Test IMU4000Driver implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = ConnectionConfig(host="*************")
        self.driver = IMU4000Driver(self.config)
    
    def test_model(self):
        """Test driver model."""
        assert self.driver.get_model() == DeviceModel.IMU4000
    
    def test_capabilities(self):
        """Test IMU4000 capabilities."""
        capabilities = self.driver.get_capabilities()
        
        assert capabilities.max_voltage == 8000.0
        assert capabilities.max_current == 4000.0
        assert WaveformType.SURGE in capabilities.supported_waveforms
        assert WaveformType.RING_WAVE in capabilities.supported_waveforms
        assert TestStandard.IEC_61000_4_12 in capabilities.supported_standards
        assert capabilities.frequency_range == (0.1, 200.0)


class TestDOW3000Driver:
    """Test DOW3000Driver implementation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = ConnectionConfig(host="*************")
        self.driver = DOW3000Driver(self.config)
    
    def test_model(self):
        """Test driver model."""
        assert self.driver.get_model() == DeviceModel.DOW3000
    
    def test_capabilities(self):
        """Test DOW3000 capabilities."""
        capabilities = self.driver.get_capabilities()
        
        assert capabilities.max_voltage == 3000.0
        assert capabilities.max_current == 1500.0
        assert WaveformType.DAMPED_OSC_SLOW in capabilities.supported_waveforms
        assert WaveformType.DAMPED_OSC_FAST in capabilities.supported_waveforms
        assert TestStandard.IEC_61000_4_18 in capabilities.supported_standards
        assert capabilities.has_current_control is False  # Voltage-only
        assert capabilities.calibration_required is False
    
    def test_set_current_not_supported(self):
        """Test that setting current raises error for DOW3000."""
        with pytest.raises(EPOSDriverError, match="does not support current control"):
            self.driver.set_current(100.0)
    
    def test_get_current_not_supported(self):
        """Test that getting current raises error for DOW3000."""
        with pytest.raises(EPOSDriverError, match="does not support current control"):
            self.driver.get_current()


class TestDriverFactory:
    """Test driver factory function."""
    
    def test_create_all_drivers(self):
        """Test creating all supported driver types."""
        config = ConnectionConfig(host="*************")
        
        # Test all device models
        driver_types = {
            DeviceModel.IMU3000: IMU3000Driver,
            DeviceModel.IMU4000: IMU4000Driver,
            DeviceModel.IMU_MG1: IMUMGDriver,
            DeviceModel.DOW3000: DOW3000Driver,
            DeviceModel.DOW_CG1: DOWCGDriver,
        }
        
        for model, expected_type in driver_types.items():
            driver = create_driver(model, config)
            assert isinstance(driver, expected_type)
            assert driver.get_model() == model
    
    def test_create_driver_invalid_model(self):
        """Test creating driver with invalid model."""
        config = ConnectionConfig(host="*************")
        
        # This would require mocking DeviceModel enum, so we'll test the error path
        # by passing an invalid model through the function
        with pytest.raises(EPOSDriverError, match="Unsupported device model"):
            # Create a mock invalid model
            invalid_model = Mock()
            invalid_model.value = "INVALID_MODEL"
            create_driver(invalid_model, config)
