#!/usr/bin/env python3
"""
Installation Script for PyEPOS Driver Library

Simple script to install dependencies and set up the development environment.
"""

import subprocess
import sys
from pathlib import Path
import os


def run_command(command, description):
    """Run a command and return success status."""
    print(f"\n{description}...")
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"✗ Command not found: {command[0]}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print(f"✗ Python 3.10+ required, found {version.major}.{version.minor}")
        return False
    
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def main():
    """Main installation function."""
    
    print("PyEPOS Driver Library Installation")
    print("=" * 50)
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Check Python version
    if not check_python_version():
        print("\nPlease upgrade Python to version 3.10 or higher")
        return 1
    
    success_count = 0
    total_count = 0
    
    # Install core dependencies
    total_count += 1
    if run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                  "Installing core dependencies"):
        success_count += 1
    
    # Install development dependencies
    total_count += 1
    if run_command([sys.executable, "-m", "pip", "install", "-e", ".[dev]"], 
                  "Installing development dependencies"):
        success_count += 1
    
    # Create directories
    directories = ["logs", "profiles", "examples/profiles"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Test installation
    total_count += 1
    if run_command([sys.executable, "-c", "import src.pyepos_driver_lib; print('Import successful')"], 
                  "Testing installation"):
        success_count += 1
    
    # Summary
    print(f"\n{'='*50}")
    print("INSTALLATION SUMMARY")
    print('='*50)
    
    if success_count == total_count:
        print("🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Review the README.md for usage instructions")
        print("2. Check examples/ directory for sample code")
        print("3. Run 'python run_tests.py' to verify everything works")
        print("4. Update device IP addresses in examples before testing with hardware")
        
        print(f"\nQuick start:")
        print(f"  python examples/test_driver.py")
        print(f"  python -m src.pyepos_driver_lib.main --help")
        
        return 0
    else:
        print("❌ Installation encountered errors")
        print("\nTroubleshooting:")
        print("1. Ensure you have Python 3.10+ installed")
        print("2. Try upgrading pip: python -m pip install --upgrade pip")
        print("3. Check internet connectivity for package downloads")
        print("4. Consider using a virtual environment")
        
        return 1


if __name__ == "__main__":
    exit(main())
