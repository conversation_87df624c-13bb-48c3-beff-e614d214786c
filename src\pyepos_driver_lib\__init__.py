"""
PyEPOS Driver Library

Core modules for EMC Partner generator control via EPOS protocol.
"""

__version__ = "0.1.0"

from .logger import <PERSON>rror<PERSON><PERSON><PERSON>, initialize_logging

# Core imports that don't require external dependencies
try:
    from .connection import DeviceConnection, ConnectionConfig
    from .commands import EPOSCommands, TestStandard, TestParameters
    from .drivers import *
except ImportError as e:
    # Some dependencies may not be installed yet
    pass



__all__ = [
    'DeviceConnection',
    'ConnectionConfig',
    'EPOSCommands',
    'TestStandard',
    'TestParameters',
    'ErrorHandler',
    'initialize_logging',
    'BaseEPOSDriver',
    'IMU3000Driver',
    'IMU4000Driver',
    'IMUMGDriver',
    'DOW3000Driver',
    'DOWCGDriver',
    'DeviceModel',
    'create_driver'
]
