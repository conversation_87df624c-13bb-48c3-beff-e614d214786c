"""
EPOS Device Drivers Module

Device-specific implementations for EMC Partner generators.
Supports IMU3000, IMU4000, IMU-MG1, DOW3000, DOW-CG1 models.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import time

from .connection import DeviceConnection, ConnectionConfig
from .commands import EPOSCommands, TestStandard, TestParameters, WaveformType
from .logger import get_logger

logger = get_logger(__name__)


class DeviceModel(Enum):
    """Supported device models."""
    IMU3000 = "IMU3000"
    IMU4000 = "IMU4000"
    IMU_MG1 = "IMU-MG1"
    DOW3000 = "DOW3000"
    DOW_CG1 = "DOW-CG1"


class DeviceStatus(Enum):
    """Device status states."""
    DISCONNECTED = "DISCONNECTED"
    CONNECTED = "CONNECTED"
    READY = "READY"
    TESTING = "TESTING"
    ERROR = "ERROR"
    CALIBRATING = "CALIBRATING"


@dataclass
class DeviceCapabilities:
    """Device capability specifications."""
    max_voltage: float  # Maximum voltage in V
    max_current: float  # Maximum current in A
    supported_waveforms: List[WaveformType]
    supported_standards: List[TestStandard]
    frequency_range: Tuple[float, float]  # Min, Max frequency in Hz
    has_current_control: bool = True
    has_temperature_monitoring: bool = True
    calibration_required: bool = False


class EPOSDriverError(Exception):
    """Exception raised for driver-related errors."""
    pass


class BaseEPOSDriver(ABC):
    """
    Abstract base class for EPOS device drivers.
    
    Defines the common interface that all device drivers must implement.
    """
    
    def __init__(self, connection_config: ConnectionConfig):
        """
        Initialize the base driver.
        
        Args:
            connection_config: Connection configuration for the device
        """
        self.connection_config = connection_config
        self.connection: Optional[DeviceConnection] = None
        self.commands = EPOSCommands()
        self.status = DeviceStatus.DISCONNECTED
        self.device_info: Dict[str, Any] = {}
        self.capabilities = self._get_device_capabilities()
        
        logger.info(f"Base driver initialized for {self.get_model().value} at {connection_config.host}")
    
    @abstractmethod
    def get_model(self) -> DeviceModel:
        """Get the device model."""
        pass
    
    @abstractmethod
    def _get_device_capabilities(self) -> DeviceCapabilities:
        """Get device-specific capabilities."""
        pass
    
    def connect(self) -> bool:
        """
        Connect to the device.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.connection = DeviceConnection(self.connection_config.host, self.connection_config.port)
            success = self.connection.connect()
            
            if success:
                self.status = DeviceStatus.CONNECTED
                self.device_info = self.connection.get_device_info()
                
                # Perform device-specific initialization
                self._initialize_device()
                
                self.status = DeviceStatus.READY
                logger.info(f"Device {self.get_model().value} connected successfully: {self.device_info}")
            
            return success
            
        except Exception as e:
            self.status = DeviceStatus.ERROR
            logger.error(f"Failed to connect to device {self.get_model().value}: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from the device."""
        if self.connection:
            self.connection.disconnect()
            self.connection = None
        
        self.status = DeviceStatus.DISCONNECTED
        logger.info(f"Device {self.get_model().value} disconnected")
    
    def is_connected(self) -> bool:
        """Check if device is connected."""
        return (self.connection is not None and 
                self.connection.is_connected() and 
                self.status != DeviceStatus.DISCONNECTED)
    
    def _initialize_device(self):
        """Perform device-specific initialization after connection."""
        try:
            # Reset device to known state
            self._send_command('reset')
            time.sleep(1)  # Allow reset to complete
            
            # Clear any existing errors
            self._send_command('clear_status')
            
            # Verify device is ready
            self._check_device_ready()
            
            logger.info(f"Device {self.get_model().value} initialization completed")

        except Exception as e:
            logger.error(f"Device {self.get_model().value} initialization failed: {str(e)}")
            raise EPOSDriverError(f"Device initialization failed: {e}")
    
    def _check_device_ready(self):
        """Check if device is ready for operation."""
        try:
            # Check for errors
            error_response = self._send_command('get_error', expected_type='error')
            if error_response['code'] != 0:
                raise EPOSDriverError(f"Device error: {error_response['message']}")
            
            # Check operation complete
            opc_response = self._send_command('operation_complete', expected_type='bool')
            if not opc_response:
                raise EPOSDriverError("Device not ready for operation")
                
        except Exception as e:
            raise EPOSDriverError(f"Device readiness check failed: {e}")
    
    def _send_command(self, command_name: str, expected_type: str = "string", **kwargs) -> Any:
        """
        Send command to device and parse response.
        
        Args:
            command_name: Name of the command
            expected_type: Expected response type
            **kwargs: Command parameters
            
        Returns:
            Parsed response
        """
        if not self.is_connected():
            raise EPOSDriverError("Device not connected")
        
        try:
            # Format command
            formatted_command = self.commands.format_command(command_name, **kwargs)
            
            # Send command and get response
            start_time = time.time()
            response = self.connection.send_command(formatted_command)
            duration_ms = (time.time() - start_time) * 1000
            
            # Log communication
            logger.debug(f"Device {self.get_model().value} communication - "
                        f"Command: {formatted_command.strip()}, "
                        f"Response: {response.strip()}, "
                        f"Duration: {duration_ms:.2f}ms")
            
            # Parse response
            parsed_response = self.commands.parse_response(response, expected_type)
            
            return parsed_response
            
        except Exception as e:
            logger.error(f"Command '{command_name}' failed on device {self.get_model().value}: {str(e)}")
            raise EPOSDriverError(f"Command '{command_name}' failed: {e}")
    
    def get_status(self) -> DeviceStatus:
        """Get current device status."""
        return self.status
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get device information."""
        return self.device_info.copy()
    
    def get_capabilities(self) -> DeviceCapabilities:
        """Get device capabilities."""
        return self.capabilities
    
    def set_voltage(self, voltage: float) -> bool:
        """
        Set output voltage.
        
        Args:
            voltage: Voltage in V
            
        Returns:
            True if successful
        """
        if voltage > self.capabilities.max_voltage:
            raise EPOSDriverError(f"Voltage {voltage}V exceeds maximum {self.capabilities.max_voltage}V")
        
        try:
            self._send_command('set_voltage', value=voltage)
            logger.info(f"Voltage set to {voltage}V on device {self.get_model().value}")
            return True
        except Exception as e:
            logger.error("Failed to set voltage", error=str(e))
            return False
    
    def get_voltage(self) -> float:
        """Get current voltage setting."""
        return self._send_command('get_voltage', expected_type='float')
    
    def set_current(self, current: float) -> bool:
        """
        Set output current.
        
        Args:
            current: Current in A
            
        Returns:
            True if successful
        """
        if not self.capabilities.has_current_control:
            raise EPOSDriverError("Device does not support current control")
        
        if current > self.capabilities.max_current:
            raise EPOSDriverError(f"Current {current}A exceeds maximum {self.capabilities.max_current}A")
        
        try:
            self._send_command('set_current', value=current)
            logger.info(f"Current set to {current}A on device {self.get_model().value}")
            return True
        except Exception as e:
            logger.error("Failed to set current", error=str(e))
            return False
    
    def get_current(self) -> float:
        """Get current setting."""
        if not self.capabilities.has_current_control:
            raise EPOSDriverError("Device does not support current control")
        return self._send_command('get_current', expected_type='float')
    
    def start_test(self) -> bool:
        """Start test execution."""
        try:
            self._send_command('start_test')
            self.status = DeviceStatus.TESTING
            logger.info(f"Test started on device {self.get_model().value}")
            return True
        except Exception as e:
            logger.error("Failed to start test", error=str(e))
            return False
    
    def stop_test(self) -> bool:
        """Stop test execution."""
        try:
            self._send_command('stop_test')
            self.status = DeviceStatus.READY
            logger.info(f"Test stopped on device {self.get_model().value}")
            return True
        except Exception as e:
            logger.error("Failed to stop test", error=str(e))
            return False


# Device-specific driver implementations

class IMU3000Driver(BaseEPOSDriver):
    """Driver for IMU3000 Surge Generator."""

    def get_model(self) -> DeviceModel:
        return DeviceModel.IMU3000

    def _get_device_capabilities(self) -> DeviceCapabilities:
        return DeviceCapabilities(
            max_voltage=6000.0,  # 6kV
            max_current=3000.0,  # 3kA
            supported_waveforms=[WaveformType.SURGE, WaveformType.TELECOM],
            supported_standards=[TestStandard.IEC_61000_4_5, TestStandard.TELECOM_SURGE],
            frequency_range=(0.1, 100.0),  # 0.1 to 100 Hz
            has_current_control=True,
            has_temperature_monitoring=True,
            calibration_required=True
        )


class IMU4000Driver(BaseEPOSDriver):
    """Driver for IMU4000 Advanced Surge Generator."""

    def get_model(self) -> DeviceModel:
        return DeviceModel.IMU4000

    def _get_device_capabilities(self) -> DeviceCapabilities:
        return DeviceCapabilities(
            max_voltage=8000.0,  # 8kV
            max_current=4000.0,  # 4kA
            supported_waveforms=[WaveformType.SURGE, WaveformType.TELECOM, WaveformType.RING_WAVE],
            supported_standards=[
                TestStandard.IEC_61000_4_5,
                TestStandard.IEC_61000_4_12,
                TestStandard.TELECOM_SURGE
            ],
            frequency_range=(0.1, 200.0),  # 0.1 to 200 Hz
            has_current_control=True,
            has_temperature_monitoring=True,
            calibration_required=True
        )


class IMUMGDriver(BaseEPOSDriver):
    """Driver for IMU-MG1 Multi-Generator."""

    def get_model(self) -> DeviceModel:
        return DeviceModel.IMU_MG1

    def _get_device_capabilities(self) -> DeviceCapabilities:
        return DeviceCapabilities(
            max_voltage=10000.0,  # 10kV
            max_current=5000.0,   # 5kA
            supported_waveforms=[
                WaveformType.SURGE,
                WaveformType.EFT,
                WaveformType.RING_WAVE,
                WaveformType.TELECOM
            ],
            supported_standards=[
                TestStandard.IEC_61000_4_5,
                TestStandard.IEC_61000_4_4,
                TestStandard.IEC_61000_4_12,
                TestStandard.TELECOM_SURGE
            ],
            frequency_range=(0.1, 1000.0),  # 0.1 to 1000 Hz
            has_current_control=True,
            has_temperature_monitoring=True,
            calibration_required=True
        )


class DOW3000Driver(BaseEPOSDriver):
    """Driver for DOW3000 Damped Oscillatory Wave Generator."""

    def get_model(self) -> DeviceModel:
        return DeviceModel.DOW3000

    def _get_device_capabilities(self) -> DeviceCapabilities:
        return DeviceCapabilities(
            max_voltage=3000.0,  # 3kV
            max_current=1500.0,  # 1.5kA
            supported_waveforms=[WaveformType.DAMPED_OSC_SLOW, WaveformType.DAMPED_OSC_FAST],
            supported_standards=[TestStandard.IEC_61000_4_18],
            frequency_range=(0.1, 500.0),  # 0.1 to 500 Hz
            has_current_control=False,  # Voltage-only control
            has_temperature_monitoring=True,
            calibration_required=False
        )


class DOWCGDriver(BaseEPOSDriver):
    """Driver for DOW-CG1 Combined Generator."""

    def get_model(self) -> DeviceModel:
        return DeviceModel.DOW_CG1

    def _get_device_capabilities(self) -> DeviceCapabilities:
        return DeviceCapabilities(
            max_voltage=5000.0,  # 5kV
            max_current=2500.0,  # 2.5kA
            supported_waveforms=[
                WaveformType.DAMPED_OSC_SLOW,
                WaveformType.DAMPED_OSC_FAST,
                WaveformType.SURGE,
                WaveformType.EFT
            ],
            supported_standards=[
                TestStandard.IEC_61000_4_18,
                TestStandard.IEC_61000_4_5,
                TestStandard.IEC_61000_4_4
            ],
            frequency_range=(0.1, 1000.0),  # 0.1 to 1000 Hz
            has_current_control=True,
            has_temperature_monitoring=True,
            calibration_required=True
        )


# Driver factory function
def create_driver(model: DeviceModel, connection_config: ConnectionConfig) -> BaseEPOSDriver:
    """
    Create a driver instance for the specified device model.

    Args:
        model: Device model
        connection_config: Connection configuration

    Returns:
        Driver instance for the specified model

    Raises:
        EPOSDriverError: If model is not supported
    """
    driver_map = {
        DeviceModel.IMU3000: IMU3000Driver,
        DeviceModel.IMU4000: IMU4000Driver,
        DeviceModel.IMU_MG1: IMUMGDriver,
        DeviceModel.DOW3000: DOW3000Driver,
        DeviceModel.DOW_CG1: DOWCGDriver,
    }

    if model not in driver_map:
        raise EPOSDriverError(f"Unsupported device model: {model.value}")

    driver_class = driver_map[model]
    return driver_class(connection_config)


# Export all driver classes
__all__ = [
    'BaseEPOSDriver',
    'IMU3000Driver',
    'IMU4000Driver',
    'IMUMGDriver',
    'DOW3000Driver',
    'DOWCGDriver',
    'DeviceModel',
    'DeviceStatus',
    'DeviceCapabilities',
    'EPOSDriverError',
    'create_driver'
]
