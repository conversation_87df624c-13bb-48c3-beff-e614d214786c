"""
Command formatting and protocol logic — builds EPOS command strings based on test profiles, metadata, and device parameters.
Based on your CommandBuilder implementation.
"""

import re
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from .logger import get_logger

logger = get_logger(__name__)


class TestStandard(Enum):
    """Supported EMC test standards."""
    IEC_61000_4_5 = "IEC 61000-4-5:2014"  # Surge Immunity
    IEC_61000_4_4 = "IEC 61000-4-4:2012"  # Electrical Fast Transients (EFT)
    IEC_61000_4_12 = "IEC 61000-4-12:2017"  # Ring Wave Immunity
    IEC_61000_4_18 = "IEC 61000-4-18:2019"  # Damped Oscillatory Waves
    TELECOM_SURGE = "Telecom Surge"  # 10/700 µs Voltage, 5/320 µs Current


class WaveformType(Enum):
    """Supported waveform types."""
    SURGE = "SURGE"
    EFT = "EFT"
    RING_WAVE = "RING"
    DAMPED_OSC_SLOW = "DOW_SLOW"
    DAMPED_OSC_FAST = "DOW_FAST"
    TELECOM = "TELECOM"


@dataclass
class TestParameters:
    """Test parameter configuration."""
    voltage: float  # Test voltage in V
    current: Optional[float] = None  # Test current in A (if applicable)
    frequency: Optional[float] = None  # Repetition frequency in Hz
    duration: Optional[float] = None  # Test duration in seconds
    polarity: str = "BOTH"  # "POS", "NEG", or "BOTH"
    coupling: str = "AC"  # "AC" or "DC"
    impedance: Optional[float] = None  # Source impedance in Ohms


class EPOSCommandError(Exception):
    """Exception raised for EPOS command errors."""
    pass


class EPOSResponseError(Exception):
    """Exception raised for EPOS response parsing errors."""
    pass


class CommandBuilder:
    """
    Command formatting and protocol logic for EPOS devices.
    Based on your original CommandBuilder implementation.
    """

    def __init__(self, device_driver):
        """
        Initialize command builder.

        Args:
            device_driver: Device driver instance
        """
        self.logger = logger
        self.device_driver = device_driver

    def build_test_command(self, device, profile):
        """
        Build test command based on device and profile.

        Args:
            device: Device instance
            profile: Test profile

        Returns:
            Formatted test command
        """
        # Use device.supported_tests() to validate profile
        # Implementation depends on specific device capabilities
        pass

    def clean_epos_response(self, response, prefix=None):
        """
        Removes EPOS command prefix from a response string.
        If prefix is provided, it strips that specific prefix.
        Otherwise, it removes the first colon-prefixed token.

        Args:
            response: Raw response string
            prefix: Optional prefix to remove

        Returns:
            Cleaned response string
        """
        if not response:
            return "UNKNOWN"

        response = response.strip()

        if prefix:
            pattern = rf'^{re.escape(prefix)}\s+'
            return re.sub(pattern, '', response)

        # Generic fallback: remove first colon-prefixed token
        return re.sub(r'^:\w+(:\w+)*\s+', '', response)

    def device_information(self):
        """
        Queries and displays the device settings.

        Returns:
            Test capabilities string
        """
        # Collect data from device
        firmware_version = self.device_driver.get_firmware_version()
        os_version = self.device_driver.get_os_version()
        device_type = self.device_driver.get_device_model()
        serial_number = self.device_driver.get_serial_number()

        # Clean responses
        firmware_version = self.clean_epos_response(firmware_version, ":SYSTEM:INFO:VERSION")
        os_version = self.clean_epos_response(os_version, ":SYSTEM:INFO:HARDWARE_TYPE")
        device_type = self.clean_epos_response(device_type, ":SYSTEM:INFO:NAME")
        serial_number = self.clean_epos_response(serial_number, ":SYSTEM:INFO:SERIAL")

        # Display to console
        print(f"\nFirmware Version: {firmware_version}")
        print(f"OS Version: {os_version}")
        print(f"Device Model Number: {device_type}")
        print(f"Serial Number: {serial_number}")

        # Request and display the test capabilities of the device
        return self.query_test_capabilities()

    def query_test_capabilities(self):
        """
        Query device test capabilities.

        Returns:
            Cleaned test capabilities response
        """
        test_capabilities = self.device_driver.get_device_test_capability()
        self.logger.debug(f"[DEVICE RESPONSE] >> {test_capabilities}\n")
        cleaned_response = self.clean_epos_response(test_capabilities, ':TEST:AVAIL')
        return cleaned_response

    def set_test_reporting(self):
        """
        Configure test reporting settings and metadata.
        """
        # Manipulate Protocol metadata
        self.device_driver.company_name = "PyEMC Labs"
        self.device_driver.operator_name = "David G."
        self.device_driver.eut_description = "Surge Immunity Test Unit"
        self.device_driver.eut_serial_number = "SN-00123"
        self.device_driver.eut_comments = "Initial test run for protocol validation"

        # Configure output formats
        self.device_driver.csv_protocol_state = True
        self.device_driver.html_protocol_state = True

        # Set save path and file name
        self.device_driver.protocol_save_path = "D:/emc_reports"
        self.device_driver.protocol_save_name = "test_run_001"

        # Confirm settings using clean_epos_response
        print("Company:", self.clean_epos_response(self.device_driver.company_name, ":PROTOCOL:GENERAL:COMPANY"))
        print("Operator:", self.clean_epos_response(self.device_driver.operator_name, ":PROTOCOL:GENERAL:OPERATOR"))
        print("CSV Enabled:", self.clean_epos_response(self.device_driver.csv_protocol_state, ":PROTOCOL:FORMAT:CSV"))
        print("HTML Enabled:", self.clean_epos_response(self.device_driver.html_protocol_state, ":PROTOCOL:FORMAT:HTML"))
        print("Save Path:", self.clean_epos_response(self.device_driver.protocol_save_path, ":PROTOCOL:SAVE:PATH"))
        print("Save Name:", self.clean_epos_response(self.device_driver.protocol_save_name, ":PROTOCOL:SAVE:NAME"))


# Legacy EPOSCommands class for framework compatibility
class EPOSCommands:
    """
    Legacy EPOS command formatter for framework compatibility.
    Delegates to CommandBuilder for actual functionality.
    """

    def __init__(self):
        """Initialize legacy commands handler."""
        self._command_history: List[str] = []
        self._response_history: List[str] = []
        self._command_builder = None

    def set_device_driver(self, device_driver):
        """Set device driver for command building."""
        self._command_builder = CommandBuilder(device_driver)

    # Standard SCPI commands
    SCPI_COMMANDS = {
        'identify': '*IDN?',
        'reset': '*RST',
        'clear_status': '*CLS',
        'operation_complete': '*OPC?',
        'self_test': '*TST?',
        'wait': '*WAI'
    }

    # EPOS-specific commands
    EPOS_COMMANDS = {
        'set_voltage': 'VOLT {value}',
        'get_voltage': 'VOLT?',
        'set_current': 'CURR {value}',
        'get_current': 'CURR?',
        'set_frequency': 'FREQ {value}',
        'get_frequency': 'FREQ?',
        'set_waveform': 'WAVEFORM {type}',
        'get_waveform': 'WAVEFORM?',
        'start_test': 'RUN:START',  # Using your command format
        'stop_test': 'RUN:STOP',   # Using your command format
        'get_test_status': 'TEST:STATUS?',
        'set_coupling': 'COUPLING {mode}',
        'get_coupling': 'COUPLING?',
        'set_polarity': 'POLARITY {pol}',
        'get_polarity': 'POLARITY?',
        'get_error': 'SYSTEM:ERROR?',
        'get_temperature': 'SYSTEM:TEMPERATURE?',
        'calibrate': 'CALIBRATION:START'
    }

    def format_command(self, command_name: str, **kwargs) -> str:
        """
        Format an EPOS command with parameters.

        Args:
            command_name: Name of the command
            **kwargs: Command parameters

        Returns:
            Formatted command string
        """
        try:
            # Check SCPI commands first
            if command_name in self.SCPI_COMMANDS:
                command = self.SCPI_COMMANDS[command_name]
            elif command_name in self.EPOS_COMMANDS:
                command = self.EPOS_COMMANDS[command_name]
            else:
                raise EPOSCommandError(f"Unknown command: {command_name}")

            # Format command with parameters
            formatted_command = command.format(**kwargs)

            # Store in history
            self._command_history.append(formatted_command)
            if len(self._command_history) > 100:  # Keep last 100 commands
                self._command_history.pop(0)

            logger.debug(f"Formatted EPOS command '{command_name}': {formatted_command}")

            return formatted_command

        except KeyError as e:
            raise EPOSCommandError(f"Missing parameter for command {command_name}: {e}")
        except Exception as e:
            raise EPOSCommandError(f"Error formatting command {command_name}: {e}")

    def parse_response(self, response: str, expected_type: str = "string") -> Any:
        """
        Parse response from EPOS device using your clean_epos_response method.

        Args:
            response: Raw response string
            expected_type: Expected response type

        Returns:
            Parsed response value
        """
        # Store in history
        self._response_history.append(response)
        if len(self._response_history) > 100:
            self._response_history.pop(0)

        # Use your cleaning method if command builder is available
        if self._command_builder:
            cleaned_response = self._command_builder.clean_epos_response(response)
        else:
            cleaned_response = response.strip()

        # Parse based on expected type
        try:
            if expected_type == "string":
                return cleaned_response
            elif expected_type == "float":
                return float(cleaned_response)
            elif expected_type == "int":
                return int(cleaned_response)
            elif expected_type == "bool":
                return cleaned_response.upper() in ['1', 'TRUE', 'ON', 'YES']
            elif expected_type == "error":
                if ',' in cleaned_response:
                    code, message = cleaned_response.split(',', 1)
                    return {'code': int(code), 'message': message.strip('"')}
                else:
                    return {'code': 0, 'message': 'No error'}
            else:
                return cleaned_response

        except ValueError as e:
            raise EPOSResponseError(f"Cannot parse response '{response}' as {expected_type}: {e}")

    def get_command_history(self) -> List[str]:
        """Get command history."""
        return self._command_history.copy()

    def get_response_history(self) -> List[str]:
        """Get response history."""
        return self._response_history.copy()

    def clear_history(self) -> None:
        """Clear command and response history."""
        self._command_history.clear()
        self._response_history.clear()
        logger.info("Cleared command and response history")


# Compatibility aliases for framework integration
EPOSCommandBuilder = CommandBuilder
