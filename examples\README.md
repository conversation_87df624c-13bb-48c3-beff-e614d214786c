# PyEPOS Driver Library Examples

This directory contains example scripts demonstrating how to use the PyEPOS Driver Library.

## Examples

### 1. basic_usage.py
Demonstrates basic connection and control of an EMC generator.

**Usage:**
```bash
python basic_usage.py
```

**Features:**
- Device connection and disconnection
- Reading device information and capabilities
- Setting voltage and current
- Starting and stopping tests
- Error handling

### 2. profile_example.py
Shows how to create, save, and execute test profiles using YAML configuration.

**Usage:**
```bash
python profile_example.py
```

**Features:**
- Creating test profiles programmatically
- Adding test steps with different parameters
- Saving profiles to YAML files
- Loading and executing profiles
- Dry-run validation

### 3. test_driver.py
Comprehensive testing of all supported device models and framework functionality.

**Usage:**
```bash
python test_driver.py
```

**Features:**
- Testing all device driver implementations
- Command generation validation
- Capability verification
- Framework readiness check

## Configuration

Before running the examples with actual hardware:

1. **Update IP Address**: Change the `host` variable in each script to match your device's IP address
2. **Check Port**: Ensure the port (default 5025) matches your device configuration
3. **Device Model**: Select the correct device model for your hardware

## Example Configuration

```python
# Device configuration
device_model = DeviceModel.IMU3000  # Change to your device
host = "*************"              # Change to your device IP
port = 5025                         # Default EPOS port

connection_config = ConnectionConfig(
    host=host,
    port=port,
    timeout=10.0,
    retry_attempts=3
)
```

## Running Without Hardware

All examples can be run without actual hardware connected. They will:
- Successfully create driver instances
- Display device capabilities
- Show command formatting
- Demonstrate profile management
- Fail gracefully when attempting to connect (expected behavior)

## Profile Directory

The profile examples create a `profiles/` subdirectory to store YAML test profiles. These profiles can be:
- Edited manually
- Shared between systems
- Version controlled
- Executed via CLI or programmatically

## CLI Usage

You can also use the command-line interface:

```bash
# Show device information
python -m pyepos_driver_lib.main info --model IMU3000 --host *************

# Run a basic test
python -m pyepos_driver_lib.main test --model IMU3000 --voltage 1000 --duration 5

# List profiles
python -m pyepos_driver_lib.main profile list

# Run a profile
python -m pyepos_driver_lib.main profile run my_profile.yaml
```

## Troubleshooting

### Connection Issues
- Verify device IP address and network connectivity
- Check that EPOS protocol is enabled on the device
- Ensure firewall allows communication on the specified port

### Import Errors
- Make sure you're running from the correct directory
- Verify all dependencies are installed: `pip install -r requirements.txt`
- Check Python path includes the src directory

### Device Compatibility
- Confirm your device model is supported
- Check device firmware version compatibility
- Verify EPOS protocol version

## Next Steps

1. **Start with test_driver.py** to verify framework functionality
2. **Try basic_usage.py** with your device configuration
3. **Explore profile_example.py** for advanced test automation
4. **Create custom profiles** for your specific test requirements
5. **Integrate into your test automation** workflows
