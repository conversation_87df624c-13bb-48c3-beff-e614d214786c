#!/usr/bin/env python3
"""
Test script to verify logger integration works correctly.
"""

import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_logger_integration():
    """Test that the integrated logger works correctly."""
    
    print("Testing Logger Integration")
    print("=" * 40)
    
    try:
        # Test importing the logger
        from pyepos_driver_lib.logger import <PERSON>rrorH<PERSON><PERSON>, get_logger, initialize_logging
        print("✓ Logger imports successful")
        
        # Test creating error handler
        error_handler = ErrorHandler()
        print("✓ ErrorHandler created successfully")
        
        # Test logging methods
        error_handler.info("Test info message")
        error_handler.warning("Test warning message")
        error_handler.debug("Test debug message")
        print("✓ Basic logging methods work")
        
        # Test error handling
        try:
            raise ValueError("Test exception")
        except Exception as e:
            error_handler.handle_error("Test error handling", e)
        print("✓ Error handling works")
        
        # Test global logger
        logger = get_logger()
        logger.info("Test global logger")
        print("✓ Global logger works")
        
        # Test initialization
        logger_instance = initialize_logging(log_level="INFO")
        logger_instance.info("Test initialized logger")
        print("✓ Logger initialization works")
        
        # Test importing from main package
        from pyepos_driver_lib import ErrorHandler as MainErrorHandler
        main_handler = MainErrorHandler()
        main_handler.info("Test main package import")
        print("✓ Main package import works")
        
        print("\n🎉 All logger integration tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Logger integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_framework_imports():
    """Test that the framework can be imported without errors."""
    
    print("\nTesting Framework Imports")
    print("=" * 40)
    
    try:
        # Test core imports
        from pyepos_driver_lib.connection import ConnectionConfig, EPOSConnection
        print("✓ Connection module imports")
        
        from pyepos_driver_lib.commands import EPOSCommands, TestStandard, TestParameters
        print("✓ Commands module imports")
        
        from pyepos_driver_lib.drivers import DeviceModel, create_driver
        print("✓ Drivers module imports")
        

        
        # Test creating basic objects
        config = ConnectionConfig(host="*************")
        print(f"✓ ConnectionConfig created: {config.host}:{config.port}")
        
        commands = EPOSCommands()
        print("✓ EPOSCommands created")
        
        # Test driver creation
        driver = create_driver(DeviceModel.IMU3000, config)
        print(f"✓ Driver created for {driver.get_model().value}")
        

        
        print("\n🎉 All framework import tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Framework import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    
    print("PyEMC Framework Integration Test")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # Test logger integration
    if test_logger_integration():
        success_count += 1
    
    # Test framework imports
    if test_framework_imports():
        success_count += 1
    
    # Summary
    print(f"\n{'='*50}")
    print("INTEGRATION TEST SUMMARY")
    print('='*50)
    print(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All integration tests passed!")
        print("\nYour PyEMC framework is ready to use!")
        print("Next steps:")
        print("1. Run 'python install.py' to install dependencies")
        print("2. Try 'python examples/test_driver.py' to test the framework")
        print("3. Update device IP addresses in examples for hardware testing")
        return 0
    else:
        print("❌ Some integration tests failed")
        return 1


if __name__ == "__main__":
    exit(main())
