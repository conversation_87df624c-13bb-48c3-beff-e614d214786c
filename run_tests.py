#!/usr/bin/env python3
"""
Test Runner for PyEPOS Driver Library

Simple script to run unit tests and examples.
"""

import subprocess
import sys
from pathlib import Path
import os


def run_command(command, description):
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print('='*60)
    
    try:
        result = subprocess.run(command, check=True, capture_output=False)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"✗ Command not found: {command[0]}")
        return False


def main():
    """Main test runner."""
    
    print("PyEPOS Driver Library Test Runner")
    print("=" * 60)
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    success_count = 0
    total_count = 0
    
    # Check if pytest is available
    try:
        subprocess.run([sys.executable, "-m", "pytest", "--version"], 
                      check=True, capture_output=True)
        pytest_available = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pytest_available = False
    
    # Run unit tests if pytest is available
    if pytest_available:
        total_count += 1
        if run_command([sys.executable, "-m", "pytest", "tests/", "-v"], 
                      "Unit Tests"):
            success_count += 1
    else:
        print("\n⚠️  pytest not available, skipping unit tests")
        print("   Install with: pip install pytest")
    
    # Run examples
    examples = [
        ("examples/test_driver.py", "Framework Test"),
        ("examples/basic_usage.py", "Basic Usage Example"),
        ("examples/profile_example.py", "Profile Management Example")
    ]
    
    for example_file, description in examples:
        if Path(example_file).exists():
            total_count += 1
            if run_command([sys.executable, example_file], description):
                success_count += 1
        else:
            print(f"\n⚠️  Example file not found: {example_file}")
    
    # Test CLI interface
    total_count += 1
    if run_command([sys.executable, "-m", "src.pyepos_driver_lib.main", "version"], 
                  "CLI Interface Test"):
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    print(f"Total Tests: {total_count}")
    print(f"Successful: {success_count}")
    print(f"Failed: {total_count - success_count}")
    
    if success_count == total_count:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    exit(main())
