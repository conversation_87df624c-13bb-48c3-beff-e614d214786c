#!/usr/bin/env python3
"""
Test script to verify your CommandBuilder integration works correctly.
"""

import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_command_builder():
    """Test that your CommandBuilder works correctly."""
    
    print("Testing Your CommandBuilder Integration")
    print("=" * 50)
    
    try:
        # Test importing your command builder
        from pyepos_driver_lib.commands import CommandBuilder, EPOSCommands
        print("✓ CommandBuilder imports successful")
        
        # Create a mock device driver for testing
        class MockDeviceDriver:
            def get_firmware_version(self):
                return ":SYSTEM:INFO:VERSION 1.2.3"
            
            def get_os_version(self):
                return ":SYSTEM:INFO:HARDWARE_TYPE Linux"
            
            def get_device_model(self):
                return ":SYSTEM:INFO:NAME IMU3000"
            
            def get_serial_number(self):
                return ":SYSTEM:INFO:SERIAL SN12345"
            
            def get_device_test_capability(self):
                return ":TEST:AVAIL SURGE,EFT,RING"
        
        mock_driver = MockDeviceDriver()
        
        # Test creating command builder
        cmd_builder = CommandBuilder(mock_driver)
        print("✓ CommandBuilder created successfully")
        
        # Test your clean_epos_response method
        test_response = ":SYSTEM:INFO:VERSION 1.2.3"
        cleaned = cmd_builder.clean_epos_response(test_response, ":SYSTEM:INFO:VERSION")
        assert cleaned == "1.2.3", f"Expected '1.2.3', got '{cleaned}'"
        print("✓ clean_epos_response method works correctly")
        
        # Test generic cleaning
        generic_cleaned = cmd_builder.clean_epos_response(":TEST:AVAIL SURGE,EFT")
        assert generic_cleaned == "SURGE,EFT", f"Expected 'SURGE,EFT', got '{generic_cleaned}'"
        print("✓ Generic response cleaning works")
        
        # Test device information method
        capabilities = cmd_builder.device_information()
        assert capabilities == "SURGE,EFT,RING", f"Expected 'SURGE,EFT,RING', got '{capabilities}'"
        print("✓ device_information method works")
        
        # Test query_test_capabilities
        test_caps = cmd_builder.query_test_capabilities()
        assert test_caps == "SURGE,EFT,RING", f"Expected 'SURGE,EFT,RING', got '{test_caps}'"
        print("✓ query_test_capabilities method works")
        
        # Test set_test_reporting (should not crash)
        cmd_builder.set_test_reporting()
        print("✓ set_test_reporting method works")
        
        print("\n🎉 Your CommandBuilder integration successful!")
        return True
        
    except Exception as e:
        print(f"✗ CommandBuilder integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_framework_compatibility():
    """Test that the framework compatibility works."""
    
    print("\nTesting Framework Compatibility")
    print("=" * 50)
    
    try:
        # Test legacy EPOSCommands class
        from pyepos_driver_lib.commands import EPOSCommands
        
        commands = EPOSCommands()
        print("✓ EPOSCommands created successfully")
        
        # Test command formatting
        formatted = commands.format_command('identify')
        assert formatted == '*IDN?', f"Expected '*IDN?', got '{formatted}'"
        print("✓ Command formatting works")
        
        # Test command with parameters
        voltage_cmd = commands.format_command('set_voltage', value=1000.0)
        assert voltage_cmd == 'VOLT 1000.0', f"Expected 'VOLT 1000.0', got '{voltage_cmd}'"
        print("✓ Parameterized commands work")
        
        # Test your RUN commands
        start_cmd = commands.format_command('start_test')
        assert start_cmd == 'RUN:START', f"Expected 'RUN:START', got '{start_cmd}'"
        print("✓ Your RUN:START command format works")
        
        stop_cmd = commands.format_command('stop_test')
        assert stop_cmd == 'RUN:STOP', f"Expected 'RUN:STOP', got '{stop_cmd}'"
        print("✓ Your RUN:STOP command format works")
        
        # Test response parsing
        response = commands.parse_response("1000.5", "float")
        assert response == 1000.5, f"Expected 1000.5, got {response}"
        print("✓ Response parsing works")
        
        # Test history
        history = commands.get_command_history()
        assert len(history) > 0, "Command history should not be empty"
        print("✓ Command history tracking works")
        
        print("\n🎉 Framework compatibility successful!")
        return True
        
    except Exception as e:
        print(f"✗ Framework compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    
    print("PyEMC Framework - Your Commands Integration Test")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test your command builder
    if test_command_builder():
        success_count += 1
    
    # Test framework compatibility
    if test_framework_compatibility():
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("YOUR COMMANDS INTEGRATION TEST SUMMARY")
    print('='*60)
    print(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 Your CommandBuilder is fully integrated!")
        print("\nYour commands.py features now available:")
        print("✓ EPOS response cleaning with regex patterns")
        print("✓ Device information querying and display")
        print("✓ Test capabilities detection")
        print("✓ Test reporting configuration")
        print("✓ Framework compatibility maintained")
        print("✓ Your RUN:START/RUN:STOP commands")
        
        print(f"\nKey features:")
        print("• clean_epos_response() - Your regex-based response cleaning")
        print("• device_information() - Comprehensive device info display")
        print("• set_test_reporting() - Protocol metadata configuration")
        print("• Framework integration - Works with existing drivers")
        
        return 0
    else:
        print("❌ Some integration tests failed")
        return 1


if __name__ == "__main__":
    exit(main())
