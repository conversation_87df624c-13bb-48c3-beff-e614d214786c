"""
EPOS Connection Module

Handles TCP/IP communication with EMC Partner generators using the EPOS protocol.
Based on your DeviceConnection implementation.
"""

import socket
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConnectionConfig:
    """Configuration for EPOS connection."""
    host: str
    port: int = 50050  # Default EPOS port
    timeout: float = 5.0
    retry_attempts: int = 3
    retry_delay: float = 1.0


class EPOSConnectionError(Exception):
    """Exception raised for EPOS connection errors."""
    pass


class EPOSTimeoutError(EPOSConnectionError):
    """Exception raised for EPOS timeout errors."""
    pass


class DeviceConnection:
    """
    Handles TCP socket communication with the EMC generator.
    Based on your original DeviceConnection implementation.
    """

    def __init__(self, ip: str, port: int):
        """
        Initialize device connection.

        Args:
            ip: Device IP address
            port: Device port
        """
        self.device_ip = ip
        self.device_port = port
        self.device_socket = None
        self.logger = logger

        logger.info(f"Device connection initialized - host: {ip}, port: {port}")
    
    def connect(self) -> bool:
        """
        Establish connection to the device.

        Returns:
            True if connection successful, False otherwise
        """
        device_id = (self.device_ip, self.device_port)
        try:
            if self.device_socket:
                self.disconnect()
            self.device_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.device_socket.settimeout(5)
            self.device_socket.connect(device_id)
            self.logger.info("Connected to device at %s:%s", self.device_ip, self.device_port)
            self.logger.debug("[DEVICE] >> Hello World!")  # DEBUG
            return True
        except socket.error as e:
            self.logger.error("Connection error: %s", e)
            print(f"[RECEIVING] >> Connection error: {e}")  # DEBUG
            self.device_socket = None
            return False
    
    def disconnect(self) -> None:
        """Disconnect from the device."""
        if self.device_socket:
            try:
                self.device_socket.close()
                self.logger.info("[RECEIVING] >> Disconnected from the device!")
                self.logger.debug("[RECEIVING] >> Disconnected from the device!")  # DEBUG
            finally:
                self.device_socket = None
    
    def safe_shutdown(self):
        """Safely shutdown the connection and stop any running tests."""
        try:
            self.stop_test()
        except Exception as e:
            self.logger.error(f"[ERROR] >> Failed to stop the test: {e}")
        finally:
            self.disconnect()

    def is_connected(self) -> bool:
        """
        Check if connection is active.

        Returns:
            True if connected, False otherwise
        """
        return self.device_socket is not None
    
    def write(self, msg: str) -> bool:
        """
        Write a command to the device.

        Args:
            msg: Command message to send

        Returns:
            True if successful, False otherwise
        """
        msg = msg.strip() + "\n"
        try:
            self.device_socket.sendall(msg.encode("utf-8"))
            return True
        except socket.error as e:
            self.logger.error(f"[ERROR] >> Error sending message: {e}")
            return False

    def read(self, retries: int = 3, timeout: float = 5.0):
        """
        Read response from the device.

        Args:
            retries: Number of retry attempts
            timeout: Timeout for each attempt

        Returns:
            Response string or None if failed
        """
        for attempt in range(retries):
            try:
                response = b""
                self.device_socket.settimeout(timeout)
                while True:
                    part = self.device_socket.recv(1024)
                    if not part:
                        break
                    response += part
                    if len(part) < 1024:
                        break
                response_str = response.decode("utf-8")
                return response_str
            except socket.timeout:
                self.logger.warning("Read attempt timed out.")
            except Exception as e:
                self.logger.error(f"Error reading response: {e}")
                break
        return None

    def query(self, msg: str):
        """
        Send a query command and read the response.

        Args:
            msg: Query message

        Returns:
            Response string or None if failed
        """
        self.logger.info(f"[QUERY] << with: {msg}")
        if not self.write(msg):
            return None
        return self.read()

    def send_command(self, command: str) -> str:
        """
        Send a command to the device and receive response.
        Compatibility method for framework integration.

        Args:
            command: Command string

        Returns:
            Response from the device

        Raises:
            EPOSConnectionError: If not connected or communication fails
        """
        if not self.is_connected():
            raise EPOSConnectionError("Not connected to device")

        response = self.query(command)
        if response is None:
            raise EPOSConnectionError(f"Failed to get response for command: {command}")

        return response

    def start_test(self):
        """
        Sends the RUN:START command to begin test execution.
        """
        self.write("RUN:START\n")

    def stop_test(self):
        """
        Sends the RUN:STOP command to halt test execution.
        """
        self.write("RUN:STOP\n")
    
    def get_device_info(self) -> Dict[str, Any]:
        """
        Get basic device information.
        
        Returns:
            Dictionary containing device information
        """
        try:
            # Standard SCPI identification command
            idn_response = self.send_command("*IDN?")
            
            # Parse the response (typically: manufacturer,model,serial,firmware)
            parts = idn_response.split(',')
            
            device_info = {
                'manufacturer': parts[0] if len(parts) > 0 else 'Unknown',
                'model': parts[1] if len(parts) > 1 else 'Unknown',
                'serial_number': parts[2] if len(parts) > 2 else 'Unknown',
                'firmware_version': parts[3] if len(parts) > 3 else 'Unknown',
                'connection_time': self._last_activity,
                'host': self.config.host,
                'port': self.config.port
            }
            
            logger.info(f"Retrieved device information: {device_info}")
            return device_info

        except Exception as e:
            logger.error(f"Failed to get device information: {str(e)}")
            return {
                'error': str(e),
                'host': self.config.host,
                'port': self.config.port
            }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


# Compatibility alias for framework integration
EPOSConnection = DeviceConnection
