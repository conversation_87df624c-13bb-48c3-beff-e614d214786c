#!/usr/bin/env python3
"""
Test script to verify just the logger integration works correctly.
"""

import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_logger_only():
    """Test that the logger works correctly."""
    
    print("Testing Logger Only")
    print("=" * 30)
    
    try:
        # Test importing the logger directly
        from pyepos_driver_lib.logger import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, get_logger, initialize_logging
        print("✓ Logger imports successful")
        
        # Test creating error handler
        error_handler = ErrorHandler()
        print("✓ <PERSON>rrorHandler created successfully")
        
        # Test logging methods
        error_handler.info("Test info message")
        error_handler.warning("Test warning message")
        error_handler.debug("Test debug message")
        print("✓ Basic logging methods work")
        
        # Test error handling
        try:
            raise ValueError("Test exception")
        except Exception as e:
            error_handler.handle_error("Test error handling", e)
        print("✓ Error handling works")
        
        # Test global logger
        logger = get_logger()
        logger.info("Test global logger")
        print("✓ Global logger works")
        
        # Test initialization
        logger_instance = initialize_logging(log_level="INFO")
        logger_instance.info("Test initialized logger")
        print("✓ Logger initialization works")
        
        print("\n🎉 Logger integration successful!")
        print("\nYour logger.py has been successfully integrated!")
        print("The framework now uses your ErrorHandler class for all logging.")
        
        return True
        
    except Exception as e:
        print(f"✗ Logger test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_logger_only()
    if success:
        print("\n✅ Logger integration complete!")
        print("Next steps:")
        print("1. Install dependencies: python install.py")
        print("2. Test full framework: python examples/test_driver.py")
    else:
        print("\n❌ Logger integration failed")
    exit(0 if success else 1)
