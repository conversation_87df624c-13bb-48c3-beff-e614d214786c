"""
Unit tests for the EPOS commands module.
"""

import pytest
from unittest.mock import Mock, patch

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyepos_driver_lib.commands import (
    EPOSCommands,
    TestStandard,
    TestParameters,
    WaveformType,
    EPOSCommandError,
    EPOSResponseError
)


class TestTestParameters:
    """Test TestParameters dataclass."""
    
    def test_default_values(self):
        """Test default parameter values."""
        params = TestParameters(voltage=1000.0)
        
        assert params.voltage == 1000.0
        assert params.current is None
        assert params.frequency is None
        assert params.duration is None
        assert params.polarity == "BOTH"
        assert params.coupling == "AC"
        assert params.impedance is None
    
    def test_custom_values(self):
        """Test custom parameter values."""
        params = TestParameters(
            voltage=2000.0,
            current=500.0,
            frequency=1.0,
            duration=30.0,
            polarity="POS",
            coupling="DC",
            impedance=50.0
        )
        
        assert params.voltage == 2000.0
        assert params.current == 500.0
        assert params.frequency == 1.0
        assert params.duration == 30.0
        assert params.polarity == "POS"
        assert params.coupling == "DC"
        assert params.impedance == 50.0


class TestEPOSCommands:
    """Test EPOSCommands class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.commands = EPOSCommands()
    
    def test_initialization(self):
        """Test commands initialization."""
        assert len(self.commands._command_history) == 0
        assert len(self.commands._response_history) == 0
    
    def test_format_scpi_command(self):
        """Test formatting SCPI commands."""
        # Test simple SCPI command
        formatted = self.commands.format_command('identify')
        assert formatted == '*IDN?'
        
        # Test reset command
        formatted = self.commands.format_command('reset')
        assert formatted == '*RST'
    
    def test_format_epos_command_with_parameters(self):
        """Test formatting EPOS commands with parameters."""
        # Test voltage command
        formatted = self.commands.format_command('set_voltage', value=1000.0)
        assert formatted == 'VOLT 1000.0'
        
        # Test current command
        formatted = self.commands.format_command('set_current', value=500.0)
        assert formatted == 'CURR 500.0'
        
        # Test waveform command
        formatted = self.commands.format_command('set_waveform', type='SURGE')
        assert formatted == 'WAVEFORM SURGE'
    
    def test_format_unknown_command(self):
        """Test formatting unknown command."""
        with pytest.raises(EPOSCommandError, match="Unknown command"):
            self.commands.format_command('unknown_command')
    
    def test_format_command_missing_parameter(self):
        """Test formatting command with missing parameter."""
        with pytest.raises(EPOSCommandError, match="Missing parameter"):
            self.commands.format_command('set_voltage')  # Missing 'value' parameter
    
    def test_command_validation(self):
        """Test command validation."""
        # Test invalid characters
        with pytest.raises(EPOSCommandError, match="invalid character"):
            self.commands._validate_command("VOLT 1000\r")
        
        with pytest.raises(EPOSCommandError, match="invalid character"):
            self.commands._validate_command("VOLT\x001000")
        
        # Test empty command
        with pytest.raises(EPOSCommandError, match="non-empty string"):
            self.commands._validate_command("")
    
    def test_command_history(self):
        """Test command history tracking."""
        # Format some commands
        self.commands.format_command('identify')
        self.commands.format_command('set_voltage', value=1000.0)
        self.commands.format_command('reset')
        
        history = self.commands.get_command_history()
        assert len(history) == 3
        assert '*IDN?' in history
        assert 'VOLT 1000.0' in history
        assert '*RST' in history
    
    def test_parse_response_string(self):
        """Test parsing string responses."""
        response = "EMC Partner,IMU3000,12345,1.0"
        parsed = self.commands.parse_response(response, "string")
        assert parsed == "EMC Partner,IMU3000,12345,1.0"
    
    def test_parse_response_float(self):
        """Test parsing float responses."""
        response = "1000.5"
        parsed = self.commands.parse_response(response, "float")
        assert parsed == 1000.5
    
    def test_parse_response_int(self):
        """Test parsing integer responses."""
        response = "42"
        parsed = self.commands.parse_response(response, "int")
        assert parsed == 42
    
    def test_parse_response_bool(self):
        """Test parsing boolean responses."""
        # Test true values
        for true_val in ['1', 'TRUE', 'ON', 'YES', 'true', 'on', 'yes']:
            parsed = self.commands.parse_response(true_val, "bool")
            assert parsed is True
        
        # Test false values
        for false_val in ['0', 'FALSE', 'OFF', 'NO', 'false', 'off', 'no']:
            parsed = self.commands.parse_response(false_val, "bool")
            assert parsed is False
    
    def test_parse_response_error(self):
        """Test parsing error responses."""
        response = "0,No error"
        parsed = self.commands.parse_response(response, "error")
        assert parsed['code'] == 0
        assert parsed['message'] == "No error"
        
        response = "123,Test error message"
        parsed = self.commands.parse_response(response, "error")
        assert parsed['code'] == 123
        assert parsed['message'] == "Test error message"
    
    def test_parse_response_device_error(self):
        """Test parsing device error responses."""
        with pytest.raises(EPOSResponseError, match="Device error"):
            self.commands.parse_response("ERROR: Invalid command", "string")
        
        with pytest.raises(EPOSResponseError, match="Device error"):
            self.commands.parse_response("ERR123: Parameter out of range", "string")
    
    def test_parse_response_invalid_type(self):
        """Test parsing response with invalid type conversion."""
        with pytest.raises(EPOSResponseError, match="Cannot parse"):
            self.commands.parse_response("not_a_number", "float")
        
        with pytest.raises(EPOSResponseError, match="Cannot parse"):
            self.commands.parse_response("not_an_integer", "int")
    
    def test_response_history(self):
        """Test response history tracking."""
        # Parse some responses
        self.commands.parse_response("response1", "string")
        self.commands.parse_response("response2", "string")
        self.commands.parse_response("response3", "string")
        
        history = self.commands.get_response_history()
        assert len(history) == 3
        assert "response1" in history
        assert "response2" in history
        assert "response3" in history
    
    def test_create_test_sequence_surge(self):
        """Test creating surge test sequence."""
        parameters = TestParameters(
            voltage=1000.0,
            current=500.0,
            frequency=1.0,
            polarity="BOTH",
            coupling="AC"
        )
        
        sequence = self.commands.create_test_sequence(TestStandard.IEC_61000_4_5, parameters)
        
        # Check that sequence contains expected commands
        sequence_str = ' '.join(sequence)
        assert '*RST' in sequence_str
        assert '*CLS' in sequence_str
        assert 'WAVEFORM SURGE' in sequence_str
        assert 'VOLT 1000.0' in sequence_str
        assert 'CURR 500.0' in sequence_str
        assert 'FREQ 1.0' in sequence_str
        assert 'COUPLING AC' in sequence_str
        assert 'POLARITY BOTH' in sequence_str
        assert '*WAI' in sequence_str
    
    def test_create_test_sequence_eft(self):
        """Test creating EFT test sequence."""
        parameters = TestParameters(
            voltage=2000.0,
            frequency=5.0,
            polarity="POS",
            coupling="DC"
        )
        
        sequence = self.commands.create_test_sequence(TestStandard.IEC_61000_4_4, parameters)
        
        # Check that sequence contains expected commands
        sequence_str = ' '.join(sequence)
        assert 'WAVEFORM EFT' in sequence_str
        assert 'VOLT 2000.0' in sequence_str
        assert 'FREQ 5.0' in sequence_str
        assert 'COUPLING DC' in sequence_str
        assert 'POLARITY POS' in sequence_str
    
    def test_create_test_sequence_without_current(self):
        """Test creating test sequence without current parameter."""
        parameters = TestParameters(
            voltage=1500.0,
            frequency=2.0
        )
        
        sequence = self.commands.create_test_sequence(TestStandard.IEC_61000_4_5, parameters)
        
        # Should not contain current command
        sequence_str = ' '.join(sequence)
        assert 'CURR' not in sequence_str
        assert 'VOLT 1500.0' in sequence_str
    
    def test_clear_history(self):
        """Test clearing command and response history."""
        # Add some history
        self.commands.format_command('identify')
        self.commands.parse_response("test", "string")
        
        assert len(self.commands.get_command_history()) > 0
        assert len(self.commands.get_response_history()) > 0
        
        # Clear history
        self.commands.clear_history()
        
        assert len(self.commands.get_command_history()) == 0
        assert len(self.commands.get_response_history()) == 0
    
    def test_history_size_limit(self):
        """Test that history is limited to 100 entries."""
        # Add more than 100 commands
        for i in range(150):
            self.commands.format_command('identify')
        
        history = self.commands.get_command_history()
        assert len(history) == 100  # Should be limited to 100
        
        # Add more than 100 responses
        for i in range(150):
            self.commands.parse_response(f"response{i}", "string")
        
        history = self.commands.get_response_history()
        assert len(history) == 100  # Should be limited to 100
