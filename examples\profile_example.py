#!/usr/bin/env python3
"""
Profile Management Example for PyEPOS Driver Library

This example demonstrates creating, saving, and executing test profiles.
"""

import sys
from pathlib import Path

# Add the src directory to the path so we can import the library
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyepos_driver_lib.connection import ConnectionConfig
from pyepos_driver_lib.drivers import DeviceModel
from pyepos_driver_lib.commands import TestStandard, TestParameters
from pyepos_driver_lib.profile_manager import ProfileManager
from pyepos_driver_lib.logger import initialize_logging


def create_sample_profile():
    """Create a sample test profile."""
    
    # Initialize profile manager
    profile_manager = ProfileManager("examples/profiles")
    
    # Device configuration
    device_model = DeviceModel.IMU3000
    connection_config = ConnectionConfig(
        host="*************",
        port=5025,
        timeout=10.0
    )
    
    # Create new profile
    profile = profile_manager.create_profile(
        name="IEC 61000-4-5 Surge Test",
        description="Standard surge immunity test according to IEC 61000-4-5:2014",
        device_model=device_model,
        connection_config=connection_config,
        version="1.0"
    )
    
    # Add test steps
    
    # Step 1: Low voltage test
    profile_manager.add_test_step(
        profile=profile,
        name="Low Voltage Surge",
        description="500V surge test, positive polarity",
        standard=TestStandard.IEC_61000_4_5,
        parameters=TestParameters(
            voltage=500.0,
            current=250.0,
            frequency=1.0,
            polarity="POS",
            coupling="AC"
        ),
        duration=10.0,
        delay_before=2.0,
        delay_after=1.0
    )
    
    # Step 2: Medium voltage test
    profile_manager.add_test_step(
        profile=profile,
        name="Medium Voltage Surge",
        description="1000V surge test, negative polarity",
        standard=TestStandard.IEC_61000_4_5,
        parameters=TestParameters(
            voltage=1000.0,
            current=500.0,
            frequency=1.0,
            polarity="NEG",
            coupling="AC"
        ),
        duration=15.0,
        delay_before=2.0,
        delay_after=1.0
    )
    
    # Step 3: High voltage test
    profile_manager.add_test_step(
        profile=profile,
        name="High Voltage Surge",
        description="2000V surge test, both polarities",
        standard=TestStandard.IEC_61000_4_5,
        parameters=TestParameters(
            voltage=2000.0,
            current=1000.0,
            frequency=0.5,
            polarity="BOTH",
            coupling="AC"
        ),
        duration=30.0,
        delay_before=5.0,
        delay_after=2.0
    )
    
    # Save profile
    profile_path = profile_manager.save_profile(profile)
    print(f"Profile saved to: {profile_path}")
    
    return profile, profile_manager


def main():
    """Profile management example."""
    
    # Initialize logging
    logger = initialize_logging(log_level="INFO", console_output=True, file_output=False)
    
    print("PyEPOS Driver Library - Profile Management Example")
    print("=" * 55)
    
    try:
        # Create sample profile
        print("Creating sample test profile...")
        profile, profile_manager = create_sample_profile()
        
        print(f"\nProfile created: {profile.name}")
        print(f"Description: {profile.description}")
        print(f"Device Model: {profile.device_model.value}")
        print(f"Steps: {len(profile.steps)}")
        
        # Display profile steps
        print(f"\nTest Steps:")
        for i, step in enumerate(profile.steps, 1):
            print(f"  {i}. {step.name}")
            print(f"     Standard: {step.standard.value}")
            print(f"     Voltage: {step.parameters.voltage}V")
            print(f"     Current: {step.parameters.current}A")
            print(f"     Duration: {step.duration}s")
            print()
        
        # List available profiles
        print("Available profiles:")
        profiles = profile_manager.list_profiles()
        for profile_info in profiles:
            print(f"  - {profile_info['name']} ({profile_info['filename']})")
            print(f"    Device: {profile_info['device_model']}")
            print(f"    Steps: {profile_info['step_count']}")
            print()
        
        # Dry run execution
        print("Performing dry run execution...")
        result = profile_manager.execute_profile(profile, dry_run=True)
        
        print(f"Dry run results:")
        print(f"  Execution ID: {result['execution_id']}")
        print(f"  Success: {result['success']}")
        print(f"  Total Steps: {result['total_steps']}")
        print(f"  Executed Steps: {result['executed_steps']}")
        print(f"  Duration: {result['duration_seconds']:.2f}s")
        
        # Display step results
        print(f"\nStep Results:")
        for step_result in result['results']:
            status = "✓" if step_result['success'] else "✗"
            print(f"  {status} Step {step_result['step_number']}: {step_result['step_name']}")
            if not step_result['success']:
                print(f"    Error: {step_result['message']}")
        
        print(f"\nProfile example completed successfully!")
        
        # Note about actual execution
        print(f"\nNote: To execute the profile on actual hardware:")
        print(f"1. Ensure your device is connected and accessible")
        print(f"2. Update the connection configuration with correct IP/port")
        print(f"3. Call: profile_manager.execute_profile(profile, dry_run=False)")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
