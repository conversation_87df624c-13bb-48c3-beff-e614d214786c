[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pyemc-epos"
version = "0.1.0"
description = "A modular Python driver library for controlling EMC Partner generators using the EPOS protocol"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "PyEMC Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "PyEMC Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.10"
dependencies = [
    "requests>=2.28.0",
    "click>=8.0.0",
    "colorama>=0.4.0",
    "typing-extensions>=4.0.0",
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "python-dotenv>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "black>=22.0",
    "flake8>=5.0",
    "mypy>=1.0",
]
docs = [
    "sphinx>=5.0",
    "sphinx-rtd-theme>=1.0",
]

[project.urls]
Homepage = "https://github.com/your-org/PYEMC_EPOS"
Documentation = "https://your-docs-link.com"
Repository = "https://github.com/your-org/PYEMC_EPOS.git"
"Bug Tracker" = "https://github.com/your-org/PYEMC_EPOS/issues"

[project.scripts]
pyepos = "pyepos_driver_lib.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=src --cov-report=html --cov-report=term-missing"
