#!/usr/bin/env python3
"""
Basic Usage Example for PyEPOS Driver Library

This example demonstrates basic connection and control of an EMC generator.
"""

import sys
import time
from pathlib import Path

# Add the src directory to the path so we can import the library
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pyepos_driver_lib.connection import ConnectionConfig
from pyepos_driver_lib.drivers import DeviceModel, create_driver
from pyepos_driver_lib.commands import TestStandard, TestParameters
from pyepos_driver_lib.logger import initialize_logging


def main():
    """Basic usage example."""
    
    # Initialize logging
    logger = initialize_logging(log_level="INFO", console_output=True, file_output=False)
    
    print("PyEPOS Driver Library - Basic Usage Example")
    print("=" * 50)
    
    # Configuration
    device_model = DeviceModel.IMU3000  # Change this to your device model
    host = "*************"  # Change this to your device IP
    port = 5025
    
    # Create connection configuration
    connection_config = ConnectionConfig(
        host=host,
        port=port,
        timeout=10.0,
        retry_attempts=3
    )
    
    try:
        # Create driver for the device
        print(f"Creating driver for {device_model.value}...")
        driver = create_driver(device_model, connection_config)
        
        # Display device capabilities
        capabilities = driver.get_capabilities()
        print(f"\nDevice Capabilities:")
        print(f"  Max Voltage: {capabilities.max_voltage}V")
        print(f"  Max Current: {capabilities.max_current}A")
        print(f"  Supported Standards: {[s.value for s in capabilities.supported_standards]}")
        print(f"  Frequency Range: {capabilities.frequency_range[0]}-{capabilities.frequency_range[1]} Hz")
        
        # Connect to device
        print(f"\nConnecting to device at {host}:{port}...")
        if driver.connect():
            print("✓ Connected successfully!")
            
            # Get device information
            device_info = driver.get_device_info()
            print(f"\nDevice Information:")
            for key, value in device_info.items():
                print(f"  {key}: {value}")
            
            # Basic operations
            print(f"\nPerforming basic operations...")
            
            # Set voltage
            test_voltage = 1000.0  # 1kV
            print(f"Setting voltage to {test_voltage}V...")
            if driver.set_voltage(test_voltage):
                print("✓ Voltage set successfully")
                
                # Read back voltage
                actual_voltage = driver.get_voltage()
                print(f"Actual voltage: {actual_voltage}V")
            
            # Set current (if supported)
            if capabilities.has_current_control:
                test_current = 100.0  # 100A
                print(f"Setting current to {test_current}A...")
                if driver.set_current(test_current):
                    print("✓ Current set successfully")
                    
                    # Read back current
                    actual_current = driver.get_current()
                    print(f"Actual current: {actual_current}A")
            
            # Test sequence
            print(f"\nRunning test sequence...")
            
            # Start test
            print("Starting test...")
            if driver.start_test():
                print("✓ Test started")
                
                # Run for 5 seconds
                print("Running test for 5 seconds...")
                time.sleep(5)
                
                # Stop test
                print("Stopping test...")
                if driver.stop_test():
                    print("✓ Test stopped")
                else:
                    print("✗ Failed to stop test")
            else:
                print("✗ Failed to start test")
            
            print(f"\nExample completed successfully!")
            
        else:
            print("✗ Failed to connect to device")
            return 1
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return 1
    
    finally:
        # Always disconnect
        if 'driver' in locals():
            print("\nDisconnecting...")
            driver.disconnect()
            print("✓ Disconnected")
    
    return 0


if __name__ == "__main__":
    exit(main())
