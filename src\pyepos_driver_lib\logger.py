"""
Simplified black box recorder for PyEMC framework.
Captures all critical events for debugging and compliance.
"""
import logging
import os
import re
from datetime import datetime


class BlackBoxLogger:
    """
    Simplified black box recorder for EMC testing.
    Records everything important while staying simple and reliable.
    """

    def __init__(self, log_file="logs/pyEMC_blackbox.log"):
        """Initialize the black box logger."""
        # Ensure log directory exists
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # Setup logger
        self.logger = logging.getLogger("pyemc_blackbox")
        self.logger.setLevel(logging.DEBUG)  # Capture everything

        # Clear any existing handlers
        self.logger.handlers.clear()

        # Simple formatter with clean paths
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # File handler - the "black box"
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # Console handler - immediate feedback
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Record startup
        self.record("SYSTEM", "Black box logger initialized")

    def record(self, category: str, message: str, data: dict = None):
        """
        Records a categorized event.

        Args:
            category: A broad category for the event (e.g., "SYSTEM", "COMM", "ERROR").
            message: A human-readable description of the event.
            data: Optional dictionary with additional context.
        """
        full_message = f"[{category}] {message}"
        if data:
            full_message += f" - Data: {data}"
        self.logger.info(full_message)

    def event(self, message: str, data: dict = None):
        """Records a generic event."""
        self.record("EVENT", message, data)

    def communication(self, direction: str, message: str, data: dict = None):
        """Records a communication event (SEND/RECV)."""
        self.record(f"COMM_{direction.upper()}", message, data)

    def error(self, message: str, data: dict = None):
        """Records an error and provides immediate console feedback."""
        self.record("ERROR", message, data)
        self.logger.error(f"RUNTIME ERROR: {message}")

    def exception(self, message: str, exc: Exception, data: dict = None):
        """Records an exception with traceback."""
        self.record("EXCEPTION", message, data)
        self.logger.exception(f"UNHANDLED EXCEPTION: {message}", exc_info=exc)


# Global logger instance for easy import.
# This makes the logger a singleton, so all modules use the same instance.
EPOS_LOGGER = BlackBoxLogger()


# Legacy compatibility functions and classes
class ErrorHandler:
    """
    Legacy ErrorHandler class for backward compatibility.
    Delegates to the BlackBoxLogger for actual functionality.
    """

    def __init__(self, log_file="logs/pyEPOS.log", level=logging.INFO):
        """Initialize error handler (delegates to BlackBoxLogger)."""
        self._logger = EPOS_LOGGER

    def info(self, message: str):
        """Log info message."""
        self._logger.record("INFO", message)

    def warning(self, message: str):
        """Log warning message."""
        self._logger.record("WARNING", message)

    def debug(self, message: str):
        """Log debug message."""
        self._logger.record("DEBUG", message)

    def error(self, message: str):
        """Log error message."""
        self._logger.error(message)

    def handle_error(self, message: str, exception: Exception = None):
        """Handle error with optional exception."""
        if exception:
            self._logger.exception(message, exception)
        else:
            self._logger.error(message)


def get_logger(name: str = None) -> logging.Logger:
    """
    Get a logger instance.

    Args:
        name: Logger name (optional)

    Returns:
        Logger instance
    """
    if name:
        return logging.getLogger(name)
    return EPOS_LOGGER.logger


def initialize_logging(log_level: str = "INFO", console_output: bool = True, file_output: bool = True) -> logging.Logger:
    """
    Initialize logging system.

    Args:
        log_level: Logging level
        console_output: Enable console output
        file_output: Enable file output

    Returns:
        Logger instance
    """
    # Set log level
    level = getattr(logging, log_level.upper(), logging.INFO)
    EPOS_LOGGER.logger.setLevel(level)

    return EPOS_LOGGER.logger
