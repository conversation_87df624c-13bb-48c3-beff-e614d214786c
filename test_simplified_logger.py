#!/usr/bin/env python3
"""
Test the simplified black box logger functionality.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_blackbox_logger():
    """Test the simplified black box logger."""
    
    print("Testing Simplified Black Box Logger")
    print("=" * 50)
    
    try:
        # Test importing the new logger
        from pyepos_driver_lib.logger import BlackBoxLogger, get_blackbox, record, error
        print("✓ BlackBoxLogger imports successful")
        
        # Test creating logger
        logger = BlackBoxLogger()
        print("✓ BlackBoxLogger created successfully")
        
        # Test basic recording
        logger.record("TEST", "Testing basic recording functionality")
        print("✓ Basic record() method works")
        
        # Test device events
        logger.device_event("IMU3000", "Connected", {"ip": "************", "port": 50500})
        print("✓ device_event() method works")
        
        # Test test events
        logger.test_event("Surge Test", "Started", {"voltage": 1000, "standard": "IEC 61000-4-5"})
        print("✓ test_event() method works")
        
        # Test communication logging
        logger.communication("SEND", "*IDN?")
        logger.communication("RECV", response="IMU3000,v1.2.3,SN12345")
        print("✓ communication() method works")
        
        # Test error logging
        try:
            raise ValueError("Test exception")
        except Exception as e:
            logger.error("Test error handling", e)
        print("✓ error() method works")
        
        # Test global functions
        record("GLOBAL", "Testing global record function")
        error("Testing global error function")
        print("✓ Global functions work")
        
        print("\n🎉 Simplified BlackBoxLogger working perfectly!")
        return True
        
    except Exception as e:
        print(f"✗ BlackBoxLogger test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """Test that legacy ErrorHandler still works."""
    
    print("\nTesting Legacy Compatibility")
    print("=" * 50)
    
    try:
        # Test legacy ErrorHandler
        from pyepos_driver_lib.logger import ErrorHandler
        
        handler = ErrorHandler()
        print("✓ Legacy ErrorHandler created successfully")
        
        # Test legacy methods
        handler.info("Legacy info message")
        handler.warning("Legacy warning message")
        handler.debug("Legacy debug message")
        handler.handle_error("Legacy error message")
        print("✓ Legacy methods work")
        
        # Test that it's actually a BlackBoxLogger underneath
        assert isinstance(handler, ErrorHandler)
        print("✓ Legacy compatibility maintained")
        
        print("\n🎉 Legacy compatibility working!")
        return True
        
    except Exception as e:
        print(f"✗ Legacy compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_log_file_creation():
    """Test that log files are created properly."""
    
    print("\nTesting Log File Creation")
    print("=" * 50)
    
    try:
        from pyepos_driver_lib.logger import BlackBoxLogger
        
        # Test with custom log file
        test_log = "test_logs/test_blackbox.log"
        logger = BlackBoxLogger(test_log)
        
        # Record some events
        logger.record("TEST", "File creation test")
        logger.device_event("TestDevice", "File test event")
        
        # Check if file was created
        if os.path.exists(test_log):
            print("✓ Log file created successfully")
            
            # Check file contents
            with open(test_log, 'r') as f:
                content = f.read()
                if "File creation test" in content and "TestDevice" in content:
                    print("✓ Log file contains expected content")
                else:
                    print("✗ Log file missing expected content")
                    return False
        else:
            print("✗ Log file was not created")
            return False
        
        # Clean up
        try:
            os.remove(test_log)
            os.rmdir("test_logs")
        except:
            pass
        
        print("\n🎉 Log file creation working!")
        return True
        
    except Exception as e:
        print(f"✗ Log file creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    
    print("PyEMC Framework - Simplified Logger Test")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test black box logger
    if test_blackbox_logger():
        success_count += 1
    
    # Test legacy compatibility
    if test_legacy_compatibility():
        success_count += 1
    
    # Test log file creation
    if test_log_file_creation():
        success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("SIMPLIFIED LOGGER TEST SUMMARY")
    print('='*60)
    print(f"Tests Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 Simplified logger is working perfectly!")
        print("\nKey improvements:")
        print("✓ Simpler code - easier to understand and modify")
        print("✓ Black box functionality - records everything important")
        print("✓ Categorized events - DEVICE, TEST, COMM, ERROR, etc.")
        print("✓ Legacy compatibility - existing code still works")
        print("✓ Global functions - easy to use from anywhere")
        print("✓ Clean output - readable timestamps and formatting")
        
        print(f"\nUsage examples:")
        print("• record('TEST', 'Surge test started', {'voltage': 1000})")
        print("• device_event('IMU3000', 'Connected', {'ip': '************'})")
        print("• communication('SEND', '*IDN?')")
        print("• error('Connection failed', exception)")
        
        return 0
    else:
        print("❌ Some logger tests failed")
        return 1


if __name__ == "__main__":
    exit(main())
